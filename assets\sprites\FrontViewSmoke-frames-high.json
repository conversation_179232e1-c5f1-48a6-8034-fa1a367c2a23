{"frames": {"window-smoke2-front-01": {"frame": {"x": 0, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-02": {"frame": {"x": 540, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-03": {"frame": {"x": 1080, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-04": {"frame": {"x": 1620, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-05": {"frame": {"x": 2160, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-06": {"frame": {"x": 2700, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-07": {"frame": {"x": 3240, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-08": {"frame": {"x": 3780, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-09": {"frame": {"x": 4320, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-10": {"frame": {"x": 4860, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-11": {"frame": {"x": 5400, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-12": {"frame": {"x": 5940, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-13": {"frame": {"x": 6480, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-14": {"frame": {"x": 7020, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-15": {"frame": {"x": 7560, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-16": {"frame": {"x": 8100, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-17": {"frame": {"x": 8640, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-18": {"frame": {"x": 9180, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-19": {"frame": {"x": 9720, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-20": {"frame": {"x": 10260, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-21": {"frame": {"x": 10800, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-22": {"frame": {"x": 11340, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-23": {"frame": {"x": 11880, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-24": {"frame": {"x": 12420, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-25": {"frame": {"x": 12960, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-26": {"frame": {"x": 13500, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-27": {"frame": {"x": 14040, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-28": {"frame": {"x": 14580, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-29": {"frame": {"x": 15120, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-30": {"frame": {"x": 15660, "y": 0, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-31": {"frame": {"x": 0, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-32": {"frame": {"x": 540, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-33": {"frame": {"x": 1080, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-34": {"frame": {"x": 1620, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-35": {"frame": {"x": 2160, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-36": {"frame": {"x": 2700, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-37": {"frame": {"x": 3240, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-38": {"frame": {"x": 3780, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-39": {"frame": {"x": 4320, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-40": {"frame": {"x": 4860, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-41": {"frame": {"x": 5400, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-42": {"frame": {"x": 5940, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-43": {"frame": {"x": 6480, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-44": {"frame": {"x": 7020, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-45": {"frame": {"x": 7560, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-46": {"frame": {"x": 8100, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-47": {"frame": {"x": 8640, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-48": {"frame": {"x": 9180, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-49": {"frame": {"x": 9720, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-50": {"frame": {"x": 10260, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-51": {"frame": {"x": 10800, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-52": {"frame": {"x": 11340, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-53": {"frame": {"x": 11880, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-54": {"frame": {"x": 12420, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-55": {"frame": {"x": 12960, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-56": {"frame": {"x": 13500, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-57": {"frame": {"x": 14040, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-58": {"frame": {"x": 14580, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-59": {"frame": {"x": 15120, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}, "window-smoke2-front-60": {"frame": {"x": 15660, "y": 960, "w": 540, "h": 960}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 540, "h": 960}, "sourceSize": {"w": 540, "h": 960}}}, "meta": {"app": "https://www.codeandweb.com/texturepacker", "version": "1.0", "image": "Output.png", "format": "RGBA8888", "size": {"w": 16200, "h": 1920}, "scale": "0.8", "smartupdate": "$TexturePacker:SmartUpdate:b82c7cd34e67f3c7d093f0b1267f4c84:53a3b3931e2166b0f17b0b68afb5c46a:ab242ca87e7871abc98cd1fb098a82c9$"}}