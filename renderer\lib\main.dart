import 'dart:convert';
import 'dart:js_interop';
import 'dart:math';
import 'package:js/js.dart';
// import 'dart:html' as html;

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renderer/Home.dart';
import 'package:scaled_app/scaled_app.dart';
// import 'package:web/web.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:simsushare_player/controllers/ClipboardController.dart';
import 'package:simsushare_player/controllers/SimController.dart';

@JS('on')
external set _onJsEvent(void Function(String event, String args, String socketUserId) f);

@JS('sendToParent')
external void sendToParent(String event, JSAny? payload);

void main() {
  ScaledWidgetsFlutterBinding.ensureInitialized(
    scaleFactor: (deviceSize) => min(deviceSize.width / 1200, deviceSize.height / 800),
  );
  Get.put(SimController());
  Get.put(ClipboardController());

  String? userId;
  // _onJsEvent = allowInterop((String event, dynamic args, String? socketUserId) {
  _onJsEvent = allowInterop((String event, dynamic args, String socketUserId) {
    print("Event: $event with given args: ${jsonDecode(args)}");
    if (userId == null && socketUserId.isNotEmpty) {
      userId = socketUserId;
    }
    // final payload = args?.first;
    // final payload = (jsonDecode(args) as List<dynamic>).first;
    var payload;
    // TODO: handle events
    final simController = Get.find<SimController>();
    switch (event) {
      case 'exercise_pause':
        simController.pause();
        break;
      case 'user_location_update':
        payload = jsonDecode(args);
        print("JSON Decoding args: $args with type: ${args.runtimeType} and decoded type: ${payload.runtimeType} and decoded value: $payload");

        // payload = { id: 'user_id', location: 'location_id' }
        if (payload["id"] == socketUserId) {
          final locationId = payload["location"];
          final location = simController.currentSim.value?.locations.firstWhereOrNull((element) => element.id == locationId);
          if (location != null) {
            simController.currentLocation.value = simController.currentSim.value?.locations.indexOf(location) as int;
          }
        }
        // _simController.currentSim.value?.locations.firstWhereOrNull((element) => element.id == event['location_id']);
        break;
      case 'play_start':
        simController.play();
        break;
      case 'play_pause':
        simController.pause();
        break;
      case 'audio_message':
        // {from: "", target: "", message: ""}
        // from is sending user, target is receiving user (empty means all), message is the audio message base64 encoded
        payload = jsonDecode(args);
        final audioBytes = base64Decode(payload["message"]);
        AudioPlayer().play(BytesSource(audioBytes)).catchError((error) {
          print("Error playing audio: $error");
        });
        break;
      case 'exercise_join_ok':
      case 'exercise_join_fail':
      case 'disconnect':
      case 'user_left':
      case 'user_joined':
      case 'user_status_update':
      case 'exercise_status_update':
      case 'set_var':
      case 'text_message':
      case 'asset_added':
      case 'asset_updated':
      case 'user_map_toggle':
        print("Not handled by renderer: $event with args: $args");
        break;
      // special events
      case 'set_socket_id':
        payload = args as String;
        socketUserId = payload;
        break;
      default:
        print("Unknown event: $event");
    }
    // Then call callback
    // callback?.call();
  });
  final simController = Get.find<SimController>();
  simController.currentLocation.listen((index) {
    print("Location changed to: $index");
    final location = simController.currentSim.value?.locations[index];
    if (location != null) {
      sendToParent(
        "player_user_location_update",
        {
          'location': {"id": location.id, "name": location.name},
          'id': userId
        }.jsify(),
      );
      /* html.window.parent?.dispatchEvent(
        html.CustomEvent(
          "player_user_location_update",
          detail: {
            'location': {"id": location.id, "name": location.name},
            'id': userId
          }.jsify(),
        ),
      ); */
    }
  });
  /* html.window.addEventListener("load", (event) {
    print("Window loaded");
    final simController = Get.find<SimController>();
    simController.currentLocation.listen((index) {
      print("Location changed to: $index");
      final location = simController.currentSim.value?.locations[index];
      if (location != null) {
        (html.window.parent as Window).dispatchEvent(
          new CustomEvent(
            "player_user_location_update",
            CustomEventInit(
              detail: {
                'location': {"id": location.id, "name": location.name},
                'id': userId
              }.jsify(),
            ),
          ),
        );
      }
    });
  }); */
  runApp(const WebRenderer());
}

class WebRenderer extends StatelessWidget {
  const WebRenderer({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'SUS Renderer',
      theme: ThemeData(
        // This is the theme of your application.
        //
        // TRY THIS: Try running your application with "flutter run". You'll see
        // the application has a purple toolbar. Then, without quitting the app,
        // try changing the seedColor in the colorScheme below to Colors.green
        // and then invoke "hot reload" (save your changes or press the "hot
        // reload" button in a Flutter-supported IDE, or press "r" if you used
        // the command line to start the app).
        //
        // Notice that the counter didn't reset back to zero; the application
        // state is not lost during the reload. To reset the state, use hot
        // restart instead.
        //
        // This works for code too, not just values: Most code changes can be
        // tested with just a hot reload.
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: false,
      ),
      home: Home(),
    );
  }
}
