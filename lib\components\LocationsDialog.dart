import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;

import 'package:file_picker/file_picker.dart' show FileType;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:get/get.dart';
import 'package:collection/collection.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/utils/picker.dart';

class LocationsDialog extends StatelessWidget {
  LocationsDialog({Key? key}) : super(key: key);

  final simNameTextController = TextEditingController();

  final _colorVisible = false.obs;
  final _colorBackgroundIndex = (-1).obs;
  final _colorBackground = (Colors.white).obs;

  Scenario? originalState;

  @override
  Widget build(BuildContext context) {
    final SimController _simController = Get.find();

    simNameTextController.text = _simController.currentSim.value!.name;

    // Updates original state only if it's null (i.e. first build)
    originalState ??= _simController.currentSim.value!;

    return Dialog(
      child: Column(
        children: [
          const Text("Locations"),
          Center(
            child: SizedBox(
              width: 250,
              child: TextField(
                controller: simNameTextController,
                decoration: const InputDecoration(hintText: "Simulation Name"),
                onChanged: (value) {
                  _simController.currentSim.value!.name = value;
                  _simController.currentSim.refresh();
                },
              ),
            ),
          ),
          const SizedBox(height: 10),
          Expanded(
            child: Obx(
              () => ListView.builder(
                scrollDirection: Axis.vertical,
                itemCount: _simController.currentSim.value!.states.length,
                itemBuilder: (context, sIndex) {
                  final state = _simController.currentSim.value!.states[sIndex];
                  return SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        Text(
                          state.name,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        ..._simController.currentSim.value!.locations.where((loc) => loc.state == state).mapIndexed(
                              (index, locationAtState) => Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8),
                                child: Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          width: 250,
                                          child: TextField(
                                            controller: TextEditingController(text: _simController.currentSim.value!.locations[index].name),
                                            onChanged: ((value) {
                                              print("Updating value $value");
                                              _simController.currentSim.value!.locations[index].name = value;
                                              print("New value ${_simController.currentSim.value!.locations[index].name}");
                                            }),
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 10),
                                    ElevatedButton(
                                      onPressed: () async {
                                        try {
                                          // await Future.delayed(Duration(milliseconds: 500), () async {});
                                          final result = await pickFiles(type: FileType.image);
                                          if (result == null) return;
                                          _simController.currentSim.value!.locations[index].color = "";
                                          if (kIsWeb) {
                                            print("Setting to base64");
                                            _simController.currentSim.value!.locations[index].image =
                                                base64Encode(result.files.first.bytes!.toList(growable: false));
                                          } else {
                                            File file = File(result.paths[0]!);
                                            _simController.currentSim.value!.locations[index].image = file.path;
                                          }
                                          _simController.currentSim.refresh();
                                        } catch (err) {
                                          print(err);
                                        }
                                      },
                                      child: const Text("Select photo from disk"),
                                      autofocus: true,
                                    ),
                                    ElevatedButton(
                                      onPressed: () async {
                                        _colorBackgroundIndex.value = index;
                                        _colorVisible.value = true;
                                      },
                                      child: const Text("Select Color"),
                                      autofocus: true,
                                    ),
                                    Visibility(
                                      child: SingleChildScrollView(
                                        child: Column(
                                          children: [
                                            ColorPicker(
                                              // pickerColor: Color(int.tryParse(_simController.currentSim.value!.locations[index].color) ?? 0xff000000),
                                              pickerColor: _colorBackground.value,
                                              onColorChanged: (color) {
                                                _colorBackground.value = color;
                                                _simController.currentSim.value!.locations[index].color = colorToHex(color);
                                                _simController.currentSim.refresh();
                                                print("COLOR: ${_simController.currentSim.value!.locations[index].color}");
                                              },
                                              labelTypes: const [],
                                              colorPickerWidth: 100,
                                            ),
                                            TextButton(
                                              onPressed: () {
                                                _simController.currentSim.value!.locations[index].color = "";
                                                _colorVisible.value = false;
                                                _colorBackgroundIndex.value = -1;
                                              },
                                              child: const Text("Cancel"),
                                            ),
                                            TextButton(
                                              onPressed: () {
                                                _simController.currentSim.value!.locations[index].image = "";
                                                _simController.currentSim.value!.locations[index].color = colorToHex(_colorBackground.value);
                                                _colorVisible.value = false;
                                                _colorBackgroundIndex.value = -1;
                                              },
                                              child: const Text("Done"),
                                            ),
                                          ],
                                        ),
                                      ),
                                      visible: _colorVisible.value && _colorBackgroundIndex.value == index,
                                    ),
                                    _simController.currentSim.value!.locations[index].color.isEmpty
                                        ? const SizedBox()
                                        : Container(
                                            color:
                                                Color(int.tryParse(_simController.currentSim.value!.locations[index].color, radix: 16) ?? 0xff000000),
                                            width: 200,
                                            height: 200,
                                          ),
                                    _simController.currentSim.value!.locations[index].image.isEmpty
                                        ? const SizedBox()
                                        : Row(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              Center(
                                                child: TextButton.icon(
                                                  onPressed: () {
                                                    _simController.currentSim.value!.locations[index].imageRotation -= (math.pi / 2);
                                                    if (_simController.currentSim.value!.locations[index].imageRotation < 0) {
                                                      _simController.currentSim.value!.locations[index].imageRotation =
                                                          (math.pi * 2) - _simController.currentSim.value!.locations[index].imageRotation;
                                                    }
                                                    _simController.currentSim.refresh();
                                                  },
                                                  icon: const Icon(Icons.rotate_left_outlined),
                                                  label: const Text("Rotate Right"),
                                                ),
                                              ),
                                              Transform.rotate(
                                                angle: _simController.currentSim.value!.locations[index].imageRotation,
                                                child: kIsWeb
                                                    ? Image.memory(
                                                        base64Decode(_simController.currentSim.value!.locations[index].image),
                                                        fit: BoxFit.contain,
                                                        height: 200,
                                                        width: 200,
                                                      )
                                                    : Image.file(
                                                        File(_simController.currentSim.value!.locations[index].image),
                                                        fit: BoxFit.contain,
                                                        height: 200,
                                                        width: 200,
                                                      ),
                                              ),
                                              Center(
                                                child: TextButton.icon(
                                                  onPressed: () {
                                                    _simController.currentSim.value!.locations[index].imageRotation += (math.pi / 2);
                                                    if (_simController.currentSim.value!.locations[index].imageRotation >= (math.pi * 2)) {
                                                      _simController.currentSim.value!.locations[index].imageRotation -= (math.pi * 2);
                                                    }
                                                    _simController.currentSim.refresh();
                                                  },
                                                  icon: const Icon(Icons.rotate_right_outlined),
                                                  label: const Text("Rotate Left"),
                                                ),
                                              ),
                                            ],
                                          ),
                                    Column(
                                      children: _simController.currentSim.value!.locations
                                          .where((element) => element.parentIndex == index)
                                          .map(
                                            (e) => GestureDetector(
                                              child: Text(e.name),
                                              onTap: () {
                                                // TODO: handle touch input to go to current location at the given state
                                              },
                                            ),
                                          )
                                          .toList(),
                                    ),
                                    Row(
                                      children: [
                                        const Text("Add location state"),
                                        DropdownButton(
                                          items: _simController.currentSim.value!.states
                                              .where((targetState) {
                                                final locationStates = _simController.currentSim.value!.locations
                                                    .where((loc) => loc.name == locationAtState.name)
                                                    .map((e) => e.state)
                                                    .toList();
                                                final stateUsed = locationStates.firstWhereOrNull((element) => element == targetState);
                                                return stateUsed == null;
                                              })
                                              .map((state) => DropdownMenuItem(
                                                    child: Text(state.name),
                                                    value: state,
                                                  ))
                                              .toList(),
                                          onChanged: (state) {
                                            _simController.currentSim.value!.locations.add(SimulationLocation(
                                                name: "${locationAtState.name} ($state)", sprites: [...locationAtState.sprites], parentIndex: index));
                                            _simController.currentSim.refresh();
                                          },
                                        ),
                                      ],
                                    )
                                  ],
                                ),
                              ),
                            ),
                        Visibility(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              GestureDetector(
                                child: const Icon(
                                  Icons.add,
                                  color: Colors.grey,
                                ),
                                onTap: () {
                                  _simController.currentSim.value!.locations.add(
                                    SimulationLocation(
                                      image: "",
                                      name: "Location ${_simController.currentSim.value!.locations.length + 1}",
                                      sprites: [],
                                      state: state.name,
                                    ),
                                  );
                                  _simController.currentSim.refresh();
                                },
                              )
                            ],
                          ),
                          visible: state == _simController.currentSim.value!.states[0],
                        )
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
          Row(
            children: [
              TextButton(
                onPressed: () {
                  _simController.currentSim.value = originalState;
                  _simController.currentSim.refresh();
                  Get.back();
                },
                child: const Text("Cancel"),
              ),
              TextButton(
                onPressed: () {
                  Get.back();
                },
                child: const Text("Save"),
              ),
            ],
          )
        ],
      ),
    );
  }
}
