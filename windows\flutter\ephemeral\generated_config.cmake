# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\scoop\\apps\\flutter\\3.19.5" FLUTTER_ROOT)
file(TO_CMAKE_PATH "E:\\Work\\new-flutter-app" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\scoop\\apps\\flutter\\3.19.5"
  "PROJECT_DIR=E:\\Work\\new-flutter-app"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\scoop\\apps\\flutter\\3.19.5"
  "FLUTTER_EPHEMERAL_DIR=E:\\Work\\new-flutter-app\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=E:\\Work\\new-flutter-app"
  "FLUTTER_TARGET=lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=true"
  "PACKAGE_CONFIG=E:\\Work\\new-flutter-app\\.dart_tool\\package_config.json"
)
