import 'package:flutter/material.dart';
import 'package:simsushare_player/utils/constants.dart';

class OrangeButton extends StatelessWidget {
  final IconData? icon;
  final String? label;
  final Function() onPressed;
  final EdgeInsetsGeometry? padding;
  final String? tooltip;
  final Color? backgroundColor;
  final bool? disabled;

  const OrangeButton({
    Key? key,
    required this.onPressed,
    this.icon,
    this.label,
    this.tooltip,
    this.padding,
    this.backgroundColor,
    this.disabled,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip ?? "",
      child: TextButton(
        onPressed: () {
          if (disabled == true) return;
          onPressed();
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 14.5),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              icon != null ? Icon(icon, size: 24, color: Colors.white) : const SizedBox(),
              SizedBox(width: icon != null && label != null ? 14 : 0),
              Text(label ?? "", style: const TextStyle(color: Colors.white, fontSize: 16)),
              if (disabled == true) ...[
                const SizedBox(width: 14),
                const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2, color: brick))
              ],
            ],
          ),
        ),
        style: TextButton.styleFrom(
          backgroundColor: disabled == true ? Colors.grey : (backgroundColor ?? brick),
          shape: const StadiumBorder(),
        ),
      ),
    );
  }
}

class TransparentButton extends StatelessWidget {
  final IconData? icon;
  final String? label;
  final Function() onPressed;
  final String? tooltip;
  final EdgeInsetsGeometry? padding;

  const TransparentButton({
    Key? key,
    required this.onPressed,
    this.icon,
    this.label,
    this.tooltip,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip ?? "",
      child: TextButton(
        onPressed: onPressed,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 14.5),
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(50)),
            border: Border.all(color: whiteBordersColor),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              icon != null ? Icon(icon, size: 24, color: Colors.white) : const SizedBox(),
              SizedBox(width: icon != null && label != null ? 14 : 0),
              Text(label ?? "", style: const TextStyle(color: Colors.white, fontSize: 16))
            ],
          ),
        ),
      ),
    );
  }
}
