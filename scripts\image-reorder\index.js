const sharp = require('sharp')
const fs = require('fs/promises')
const path = require('path')
const { argv } = require('process')

;
(async () => {
  console.log(argv)
  const name = argv[2];
  const spritesDir = await fs.readdir(path.resolve(__dirname, '../../assets/sprites/'))
  const outputMeta = {
    meta: {
      app: "https://simsushare.com/image-reorder",
      version: "1.0",
      image: "frames-high.png",
      format: "RGBA8888",
      scale: "1"
    },
    frames: {

    }
  };
  const matches = spritesDir.filter((fName) => fName.startsWith(name)).filter((fName) => fName.endsWith(".png")).map((fName) => fName.substring(0, fName.length - 4)).sort()
  console.log({matches})
  let largestWidth = 0;
  let largestHeight = 0;
  let totalWidth = 0;
  let totalFrameKeys = 0;
  const compositeList = [];
  for(let name of matches) {
    const image = await fs.readFile(path.resolve(__dirname, '../../assets/sprites/', name + '.png'))
    const metaFile = await fs.readFile(path.resolve(__dirname, '../../assets/sprites/', name + '.json'))
    const meta = JSON.parse(metaFile)
    const frameKeys = Object.keys(meta.frames)
    totalFrameKeys += frameKeys.length;
    // await Promise.all(frameKeys.map(async (fk, index) => {
    for (let index = 0; index < frameKeys.length; index++) {
      const fk = frameKeys[index]
      // const fk = frameKeys[index]
      const frameEntry = meta.frames[fk];
      // console.log({frameEntry})
      // const xPos = totalWidth;
      let buf = sharp(image)
      if(frameEntry.frame.h > largestHeight) {
        largestHeight = frameEntry.frame.h
      }
      if(frameEntry.frame.w > largestWidth) {
        largestWidth = frameEntry.frame.w
      }
      if(frameEntry.rotated) {
        outputMeta.frames[fk] = {
          frame: {
            // x: xPos,
            x: -1,
            y: 0,
            w: frameEntry.frame.w,
            h: frameEntry.frame.h,
          },
          rotated: false,
          trimmed: true,
        }
        totalWidth += frameEntry.frame.h
        buf = buf.extract({
          left: frameEntry.frame.x,
          top: frameEntry.frame.y,
          width: frameEntry.frame.h,
          height: frameEntry.frame.w,
        })
        .rotate(-90)
      } else {
        outputMeta.frames[fk] = {
          frame: {
            // x: xPos,
            x: -1,
            y: 0,
            w: frameEntry.frame.w,
            h: frameEntry.frame.h,
          },
          rotated: false,
          trimmed: true,
        }
        totalWidth += frameEntry.frame.w
        buf = buf.extract({
          left: frameEntry.frame.x,
          top: frameEntry.frame.y,
          width: frameEntry.frame.w,
          height: frameEntry.frame.h,
        })
      }
      const m = await sharp(await buf.toBuffer()).metadata()
      const iPos = parseInt(fk.split("/")[1])
      // console.log(m.width, "x", m.height, fk, index)
      console.log(m.width, "x", m.height, fk, iPos)
      compositeList[iPos] = { left: 0, input: await buf.png().toBuffer(), top: 0 }
    }
    // }))
  }
  const mkeys = Object.keys(outputMeta.frames).sort((a, b) => parseInt(a.split("/")[1]) - parseInt(b.split("/")[1]))
  // console.log(mkeys)
  const orderedFrames = {}
  mkeys.forEach((key, index) => {
    orderedFrames[key] = outputMeta.frames[key]
  });
  delete outputMeta.frames
  outputMeta.frames = orderedFrames
  mkeys.forEach((key, index) => {
    outputMeta.frames[key].frame.x = index * largestWidth
  })
  compositeList.forEach((value, index) => {
    compositeList[index] = {...value, left: index * largestWidth}
  })
  /* frameKeys.forEach((fk, index) => {
    outputMeta.frames[fk] = {
      frame: {
        x: largestWidth * index,
        y: 0,
        w: largestWidth,
        h: largestHeight,
      },
      rotated: false,
      trimmed: true,
    }
  }) */
  // return console.log({
  //   largestHeight,
  //   largestWidth,
  //   finalWidth: largestWidth * frameKeys.length,
  //   frameKeysLength: frameKeys.length,
  //   // compositeList,
  // })
  console.log({
    largestWidth,
    largestHeight,
    // width: largestWidth * frameKeys.length,
    width: largestWidth * totalFrameKeys,
    height: largestHeight,
    channels: 4,
    background: "#00000000"
  })
  outputMeta.meta.size = {
    // w: largestWidth * frameKeys.length,
    w: largestWidth * totalFrameKeys,
    h: largestHeight,
    totalWidth,
  }
  await sharp({
    create: {
      // width: largestWidth * frameKeys.length,
      // width: totalWidth,
      width: outputMeta.meta.size.w,
      height: largestHeight,
      channels: 4,
      background: "#00000000"
    }
  })
  .composite(compositeList)
  .png()
  .toFile(path.resolve(__dirname, name + ".png" /* , 'output-frames-high.png' */))
  await fs.writeFile(path.resolve(__dirname, name + '.json' /* 'output-frames-high.json' */), JSON.stringify(outputMeta, null, 2))
})()

// sharp({
//   create: {

//   }
// })