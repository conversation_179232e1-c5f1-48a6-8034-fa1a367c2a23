import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:simsushare_player/models/Mask.dart';
import 'package:simsushare_player/models/SimObjects.dart';

Future<ByteData?> getSpriteMasksByteData(double canvasWidth, double canvasHeight, SimSprite sprite, List<Mask> masks) async {
  if (sprite.maskIds.isEmpty) {
    return null;
  }
  final recorder = PictureRecorder();
  final canvas = Canvas(recorder, Rect.fromPoints(const Offset(0, 0), Offset(canvasWidth, canvasHeight)));
  for (var maskId in sprite.maskIds) {
    final mask = masks.firstWhere((m) => m.id == maskId);
    // final color = Color(int.parse(mask.color, radix: 16)).withOpacity(1);
    const color = Colors.black;
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..color = color;
    final path = Path();
    path.addPolygon(mask.coordinates.map((coor) => Offset(coor.x, coor.y)).toList(), true);
    canvas.drawPath(path, paint);
  }
  final maskPicture = recorder.endRecording();
  final maskImg = await maskPicture.toImage(canvasWidth.toInt(), canvasHeight.toInt());
  return maskImg.toByteData(format: ImageByteFormat.png);
}
