import 'dart:typed_data';

class File {
  final String path;
  const File(this.path);

  Future<bool> exists() => throw Exception("File class is not supported on web");
  Future<Uint8List> readAsBytes() => throw Exception("File class is not supported on web");
  Future<void> delete() => throw Exception("File class is not supported on web");
  void deleteSync() => throw Exception("File class is not supported on web");
}

Future<void> copyPath(String from, String to) => throw Exception("`copyPath` is not supported on web");
