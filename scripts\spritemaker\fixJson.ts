import * as fs from 'fs';
import * as path from 'path';

const jsonPath = path.join(__dirname, 'output');
const fileName = fs.readdirSync(jsonPath).find((file) => file.endsWith('.json'));
if(!fileName) {
  throw new Error("No json file found in " + jsonPath);
}

const data = JSON.parse(fs.readFileSync(path.join(jsonPath, fileName), 'utf8'));
const updatedFrames = {} as Record<string, any>;
Object.keys(data.frames).forEach((frameKey) => {
    const frame = data.frames[frameKey];
    const frameSplit = frameKey.split("/");
    const newFrameKey = frameSplit[0] + "/" + parseInt(frameSplit[1]);
    updatedFrames[newFrameKey] = frame;
})

data.frames = updatedFrames;

fs.writeFileSync(path.join(jsonPath, fileName), JSON.stringify(data, null, 2), 'utf8');

