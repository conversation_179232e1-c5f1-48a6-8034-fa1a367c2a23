import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/controllers/UserController.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/pages/home_views/my_simulations_view/dialogs/new_simulation_dialog.dart';
import 'package:simsushare_player/utils/constants.dart';

class MySimulationsViewTopBar extends StatelessWidget {
  final UserController userController;
  final dynamic getSimulations;
  final List<Scenario> selectedScenarios;
  final Function(String searchQuery) onSearch;

  MySimulationsViewTopBar({
    Key? key,
    required this.userController,
    required this.getSimulations,
    required this.selectedScenarios,
    required this.onSearch,
  }) : super(key: key);

  final searching = false.obs;

  @override
  Widget build(BuildContext context) {
    if (context.isMobileScreen) {
      return Obx(() => AnimatedSwitcher(
          duration: kThemeAnimationDuration,
          child: searching.value
              ? Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: const Color.fromARGB(95, 255, 255, 255)),
                    borderRadius: BorderRadius.circular(50),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: Row(
                    children: [
                      const Icon(Icons.search, color: Colors.white60),
                      Expanded(
                        child: TextField(
                          autofocus: true,
                          onChanged: (value) {
                            onSearch(value);
                          },
                          decoration: const InputDecoration(
                            hintText: "Search",
                            hintStyle: TextStyle(color: Colors.white60),
                            border: InputBorder.none,
                            focusedBorder: InputBorder.none,
                            errorBorder: InputBorder.none,
                            enabledBorder: InputBorder.none,
                            disabledBorder: InputBorder.none,
                            focusedErrorBorder: InputBorder.none,
                            contentPadding: EdgeInsets.symmetric(horizontal: 6),
                          ),
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                      IconButton(
                        onPressed: () {
                          searching.value = false;
                          onSearch("");
                        },
                        icon: const Icon(Icons.close, color: Colors.white60),
                      ),
                    ],
                  ),
                )
              : AppBar(
                  leading: null,
                  backgroundColor: Colors.black,
                  title: const Text(
                    "My Simulations",
                  ),
                  actions: [
                    // Container(
                    //   margin: const EdgeInsets.symmetric(vertical: 5),
                    //   decoration: BoxDecoration(
                    //     color: brick,
                    //     borderRadius: BorderRadius.circular(50),
                    //   ),
                    //   child: IconButton(
                    //     onPressed: () {
                    //       Get.dialog(NewSimulationDialog());
                    //     },
                    //     icon: const Icon(Icons.add),
                    //   ),
                    // ),
                    IconButton(
                      onPressed: () {
                        searching.value = true;
                      },
                      icon: const Icon(Icons.search),
                    ),
                    PopupMenuButton(
                      color: sidebarDark,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        child: const Icon(
                          Icons.more_vert,
                          color: Colors.white,
                        ),
                      ),
                      itemBuilder: (context) {
                        return generalMoreActions
                            .mapIndexed(
                              (index, element) => PopupMenuItem(
                                value: element,
                                enabled: (index != 4 || index != 5 || userController.selectedSims.keys.isNotEmpty),
                                child: Row(
                                  children: [
                                    Icon(
                                      generalMoreIcons[index],
                                      color: popupMenuIconColor,
                                      size: popupMenuIconSize,
                                    ),
                                    const SizedBox(width: 10),
                                    Text(
                                      element,
                                      style: ((index == 2 || index == 3 || index == 4) && userController.user.value == null) ||
                                              ((index == 4 || index == 5) && userController.selectedSims.keys.isEmpty)
                                          ? popMenuTextStyle.copyWith(color: Colors.grey)
                                          : popMenuTextStyle,
                                    )
                                  ],
                                ),
                                onTap: () async {
                                  return Future.delayed(const Duration(milliseconds: 200), () async {
                                    final refresh = await generalMoreHandlers(index, selectedScenarios);
                                    if (refresh != null && refresh) {
                                      getSimulations();
                                    }
                                  });
                                },
                              ),
                            )
                            .toList();
                      },
                    ),
                  ],
                )));
    }
    Widget simulationsText = Text(
      "My Simulations",
      style: defaultTextStyle.copyWith(fontSize: 24),
    );

    /* final categories = DropdownButton(
      underline: const SizedBox(),
      dropdownColor: const Color(0xFF101213),
      value: "",
      items: [
        DropdownMenuItem(
          child: Container(
            padding: const EdgeInsets.all(0.0),
            child: const Text(
              "All Categories",
              style: TextStyle(color: Colors.white, fontSize: 12),
            ),
          ),
          value: "",
          onTap: () {},
        )
      ],
      onChanged: (selectedItem) {},
    ); */

    Widget topActions = Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        simulationsText,
        const SizedBox(width: 12),
        // categories,
        Obx(
          () => searching.value
              ? Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: const Color.fromARGB(95, 255, 255, 255)),
                    borderRadius: BorderRadius.circular(50),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: Row(
                    children: [
                      const Icon(Icons.search, color: Colors.white60),
                      SizedBox(
                        width: 250,
                        child: TextField(
                          autofocus: true,
                          onChanged: (value) {
                            onSearch(value);
                          },
                          decoration: const InputDecoration(
                            hintText: "Search",
                            hintStyle: TextStyle(color: Colors.white60),
                            border: InputBorder.none,
                            focusedBorder: InputBorder.none,
                            errorBorder: InputBorder.none,
                            enabledBorder: InputBorder.none,
                            disabledBorder: InputBorder.none,
                            focusedErrorBorder: InputBorder.none,
                            contentPadding: EdgeInsets.symmetric(horizontal: 6),
                          ),
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                      IconButton(
                        onPressed: () {
                          searching.value = false;
                          onSearch("");
                        },
                        icon: const Icon(Icons.close, color: Colors.white60),
                      ),
                    ],
                  ),
                )
              : IconButton(
                  onPressed: () {
                    searching.value = true;
                  },
                  icon: const Icon(Icons.search, color: Colors.white),
                ),
        )
        // const VerticalDivider(
        //   color: Colors.white,
        // ),
        // const Icon(Icons.search, color: Colors.white),
      ],
    );

    Widget moreAndNewSimulation = Row(
      children: [
        //More
        Container(
          decoration: BoxDecoration(border: Border.all(color: const Color.fromARGB(95, 255, 255, 255)), borderRadius: BorderRadius.circular(50)),
          child: PopupMenuButton(
            color: sidebarDark,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 14.5),
              decoration: const BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(50))),
              child: const Row(
                children: [
                  Text(
                    "More",
                    style: TextStyle(color: Colors.white, fontSize: 14),
                  ),
                  Icon(
                    Icons.more_vert,
                    color: Colors.white,
                    size: 24,
                  )
                ],
              ),
            ),
            itemBuilder: (context) {
              return generalMoreActions
                  .mapIndexed(
                    (index, element) => PopupMenuItem(
                      value: element,
                      enabled: (index != 4 || index != 5)
                          ? true
                          : userController.selectedSims.keys.isNotEmpty
                              ? true
                              : false,
                      child: Row(
                        children: [
                          Icon(
                            generalMoreIcons[index],
                            color: popupMenuIconColor,
                            size: popupMenuIconSize,
                          ),
                          const SizedBox(width: 10),
                          Text(
                            element,
                            style: ((index == 2 || index == 3 || index == 4) && userController.user.value == null) ||
                                    ((index == 4 || index == 5) && userController.selectedSims.keys.isEmpty)
                                ? popMenuTextStyle.copyWith(color: Colors.grey)
                                : popMenuTextStyle,
                          )
                        ],
                      ),
                      onTap: () async {
                        return Future.delayed(const Duration(milliseconds: 200), () async {
                          final refresh = await generalMoreHandlers(index, selectedScenarios);
                          if (refresh != null && refresh) {
                            getSimulations();
                          }
                        });
                      },
                    ),
                  )
                  .toList();
            },
          ),
        ),
        const SizedBox(width: 12),
        //"New Simulation" button
        OrangeButton(
          icon: Icons.add,
          label: "New Simulation",
          onPressed: () async {
            return Get.dialog(NewSimulationDialog());
          },
        ),
      ],
    );

    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: 27,
        horizontal: 44,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          topActions,
          moreAndNewSimulation,
        ],
      ),
    );
  }
}
