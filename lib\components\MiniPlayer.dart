import 'package:flame/components.dart';
import 'package:flame/game.dart';
import 'package:flutter/material.dart';
import 'package:flame/rendering.dart' as frendering;
import 'package:simsushare_player/models/SimObjects.dart';

class MiniPlayer extends StatelessWidget {
  final SimSprite sprite;
  final double width;
  final double height;
  final bool play;
  const MiniPlayer({Key? key, required this.sprite, this.width = 100, this.height = 100, this.play = true}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: GameWidget(game: _SpritePreview(sprite: sprite, dimentions: Size(width, height), play: play)),
    );
  }
}

class _SpritePreview extends FlameGame {
  final SimSprite sprite;
  final Size dimentions;
  final bool play;
  _SpritePreview({required this.sprite, this.dimentions = const Size(100, 100), this.play = true});

  @override
  Color backgroundColor() => const Color(0x00000000);

  @override
  Future<void> onLoad() async {
    double largestWidth = 0;
    double largestHeight = 0;
    final scaleDiffWidth = sprite.img.width / sprite.width;
    final scaleDiffHeight = sprite.img.height / sprite.height;
    // print("sprite.img: ${sprite.img.width}x${sprite.img.height} and sprite: ${sprite.width}x${sprite.height} for ${sprite.assetName}");
    final animData = SpriteAnimationData(
      sprite.frames.map((frame) {
        if (frame.width > largestWidth) largestWidth = frame.width;
        if (frame.height > largestHeight) largestHeight = frame.height;
        return SpriteAnimationFrameData(
          srcPosition: Vector2(frame.x * scaleDiffWidth, frame.y * scaleDiffHeight),
          srcSize: Vector2(frame.width * scaleDiffWidth, frame.height * scaleDiffHeight),
          stepTime: 0.05 /* (1 / sprite.framerate) */,
        );
      }).toList(),
      loop: true,
    );
    largestWidth *= scaleDiffWidth;
    largestHeight *= scaleDiffHeight;
    final anim = SpriteAnimation.fromFrameData(sprite.img, animData);
    largestWidth *= scaleDiffWidth;
    largestHeight *= scaleDiffHeight;
    add(
      SpriteAnimationComponent(
        animation: anim,
        // size: Vector2(largestWidth, largestHeight),
        size: Vector2(dimentions.width, dimentions.height),
        anchor: Anchor.center,
        position: Vector2(size.x / 2, size.y / 2),
        playing: play,
      )
        ..tint(sprite.filterColor)
        ..setOpacity(sprite.opacity)
        ..decorator.addLast(frendering.PaintDecorator.blur(sprite.blur)),
    );
  }
}

class ComponentMiniPlayer extends StatelessWidget {
  final PositionComponent component;
  final double width;
  final double height;
  final bool play;
  const ComponentMiniPlayer({Key? key, required this.component, this.width = 100, this.height = 100, this.play = true}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: GameWidget(
        game: _ComponentPreview(
          component: component,
          dimentions: Size(width, height),
          play: play,
        ),
      ),
    );
  }
}

class _ComponentPreview extends FlameGame /* with HasDraggableComponents, HasTappableComponents */ {
  final PositionComponent component;
  final Size dimentions;
  final bool play;
  _ComponentPreview({required this.component, this.dimentions = const Size(100, 100), this.play = true});

  @override
  Color backgroundColor() => const Color(0x00000000);

  @override
  Future<void> onLoad() async {
    add(component);
  }
}
