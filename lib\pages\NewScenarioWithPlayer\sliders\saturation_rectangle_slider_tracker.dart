import 'package:flutter/material.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/sliders/custom_color_picker.dart';

class SaturationRectSliderTrackShape extends SliderTrackShape {
  final ColorController colorController;
  final double radius;

  SaturationRectSliderTrackShape({required this.colorController, this.radius = 2});
  
  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final double trackHeight = sliderTheme.trackHeight!;
    final double trackLeft = offset.dx;
    final double trackTop = offset.dy + (parentBox.size.height - trackHeight) / 2;
    final double trackWidth = parentBox.size.width;
    return Rect.fromLTWH(trackLeft, trackTop, trackWidth, trackHeight);
  }

  @override
  void paint(
    PaintingContext context,
    Offset offset, {
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required Animation<double> enableAnimation,
    required Offset thumbCenter,
    Offset? secondaryOffset,
    bool isDiscrete = false,
    bool isEnabled = false,
    required TextDirection textDirection,
  }) {
    if (sliderTheme.trackHeight == null) {
      return;
    }

    final Gradient gradient = LinearGradient(
      colors: [
        HSVColor.fromAHSV(1.0, colorController.hue.value, 0.25, 1.0).toColor(),
        HSVColor.fromAHSV(1.0, colorController.hue.value, 1.0, 1.0).toColor(),
      ],
    );

    final Paint paint = Paint()..shader = gradient.createShader(Rect.fromLTWH(offset.dx, offset.dy, parentBox.size.width, sliderTheme.trackHeight!));

    final Rect trackRect = getPreferredRect(
      parentBox: parentBox,
      offset: offset,
      sliderTheme: sliderTheme,
      isEnabled: isEnabled,
      isDiscrete: isDiscrete,
    );

    final RRect roundedTrackRect = RRect.fromRectAndRadius(trackRect, Radius.circular(radius));

    context.canvas.drawRRect(roundedTrackRect, paint);
  }
}
