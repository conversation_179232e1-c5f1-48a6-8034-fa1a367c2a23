import 'dart:convert';
import 'dart:io';

import 'package:file_picker/file_picker.dart' show FileType;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:simsushare_player/components/GreyButton.dart';
import 'package:simsushare_player/components/LibraryDialog.dart';
import 'package:simsushare_player/utils/constants.dart';
import 'package:simsushare_player/utils/picker.dart';

class PickLocationImage extends StatelessWidget {
  final Function(List<String?> image) onImageSelected;
  final bool multi;

  const PickLocationImage({
    Key? key,
    required this.onImageSelected,
    required this.multi,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Flex(
      direction: isMobileScreen ? Axis.vertical : Axis.horizontal,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        GreyButtonLarge(
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.upload,
                color: Colors.white,
                size: 20,
              ),
              SizedBox(width: 14),
              Text(
                "Upload From Disk",
                style: TextStyle(fontSize: 14, color: Colors.white),
              ),
            ],
          ),
          onPressed: () async {
            try {
              // await Future.delayed(Duration(milliseconds: 500), () async {});
              final result = await pickFiles(type: FileType.image, allowMultiple: multi);
              print("Pick location image result: $result");
              if (result == null) {
                print("No image file selected");
                return;
              }
              if (kIsWeb) {
                return onImageSelected(result.files.map((f) => base64Encode(f.bytes!.toList(growable: false))).toList());
              } else {
                return onImageSelected(result.paths);
              }
              /* result.files.forEachIndexed((index, currentFile) {
                SimulationLocation newSimLocation;
                if (kIsWeb) {
                  print("Setting to base64");
                  onImageSelected([base64Encode(currentFile.bytes!.toList(growable: false))]);
                  // _simController.currentSim.value!.locations.add();
                  // _initialImageAsString.value = base64Encode(result.files.first.bytes!.toList(growable: false));
                  // location.image = base64Encode(result.files.first.bytes!.toList(growable: false));
                } else {
                  File file = File();
                  _initialImage.value = file;
                  // _simController.currentSim.value!.locations[index].image = file.path;
                  newSimLocation = SimulationLocation(
                    name: "Location $locationCount",
                    sprites: [],
                    image: file.path,
                  );
                  // _simController.currentSim.value!.locations.add();
                  // location.image = file.path;
                }
                final simLocationsToAdd = _simController.currentSim.value!.states.map((state) {
                  final sl = newSimLocation.copy();
                  sl.state = state.id;
                  return sl;
                });
                _simController.currentSim.value!.locations.addAll(simLocationsToAdd);
                locationCount.value++;
              });
              _simController.currentSim.refresh();
              print("Locations: ${_simController.currentSim.value!.locations}"); */
            } catch (err) {
              print(err);
            }
          },
        ),
        const SizedBox(width: 12),
        const Text("OR", style: TextStyle(fontSize: 14, color: Colors.white)),
        const SizedBox(width: 12),
        GreyButtonLarge(
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.image, size: 20, color: Colors.white),
              SizedBox(width: 14),
              Text(
                "Select From Library",
                style: TextStyle(fontSize: 14, color: Colors.white),
              ),
            ],
          ),
          onPressed: () async {
            List<FileSystemEntity>? files = await showDialog(
              context: context,
              builder: ((context) {
                return LibraryDialog(
                  allowMultiple: multi,
                  extensionFilter: const [".jpg", ".jpeg", ".gif", ".png"],
                );
              }),
            );
            if (files == null || files.isEmpty) return;
            return onImageSelected(files.map((file) => file.path).toList());
            /* final newSimLocation = SimulationLocation(
              name: "Location $locationCount",
              sprites: [],
              image: file.path,
            );
            final simLocationsToAdd = _simController.currentSim.value!.states.map((state) {
              final sl = newSimLocation.copy();
              sl.state = state.id;
              return sl;
            });
            _simController.currentSim.value!.locations.addAll(simLocationsToAdd);
            locationCount.value++;
            _simController.currentSim.refresh(); */
          },
        ),
      ],
    );
  }
}
