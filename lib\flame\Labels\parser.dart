import 'dart:ui';

import 'package:flame/image_composition.dart';
import 'package:flutter/material.dart';
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/utils/constants.dart';
import 'package:simsushare_player/utils/web_parser.dart';

SimLabel parseLabel(String type, String text, Map<String, String> props) {
  final data = text.split(",");
  int tIndex = -1;
  final lTypes = labelToTypeMapping.values.toList();
  for (int i = 0; i < lTypes.length; i++) {
    if (lTypes[i] == type) {
      tIndex = i;
      break;
    }
  }
  return SimLabel(
    type: labelToTypeMapping.keys.toList()[tIndex],
    id: props["id"] ?? "",
    name: props["name"] ?? props["id"] ?? "",
    variables: {
      "Label Type": int.tryParse(data[4]) ?? 0,
      "Material Number": props["numbers"],
    },
    x: double.tryParse(props["x"] ?? "") ?? 0.1,
    y: double.tryParse(props["y"] ?? "") ?? 0.1,
  )
    ..filterColor = props["color"] != null
        ? getColorFromValue(int.parse(props["color"] as String), withAlpha: true)
        : (double.tryParse(data[3]) != null ? HSLColor.fromAHSL(0.7, 0, 0, (double.parse(data[3]) + 1.0) / 2).toColor() : Colors.transparent)
    ..widthScale = double.tryParse(props["scaleX"] ?? "") ?? 1.0
    ..heightScale = double.tryParse(props["scaleY"] ?? "") ?? 1.0
    ..fadeInWhen = double.tryParse(data[5]) ?? 0.0
    ..fadeInDuration = double.tryParse(data[6]) ?? 0.0
    ..mirrorY = data[12] == "true"
    ..mirrorX = data[13] == "true"
    ..widthScale = double.tryParse(data[14]) ?? 1.0
    ..blur = double.tryParse(data[15]) ?? 0.0
    ..fadeOut = data[16] == "true"
    ..fadeOutWhen = double.tryParse(data[17]) ?? 0.0
    ..fadeOutDuration = double.tryParse(data[18]) ?? 0.0;
  // just in case we need them later
  // ..mirrorY = (data.length > 12 ? data[12] : "false") == "true"
  // ..mirrorX = (data.length > 13 ? data[13] : "false") == "true"
  // ..widthScale = double.tryParse((data.length > 14 ? data[14] : "1.0")) ?? 1.0
  // ..blur = double.tryParse((data.length > 15 ? data[15] : "0.0")) ?? 0.0
  // ..fadeOut = (data.length > 16 ? data[16] : "false") == "true"
  // ..fadeOutWhen = double.tryParse((data.length > 17 ? data[17] : "0.0")) ?? 0.0
  // ..fadeOutDuration = double.tryParse((data.length > 18 ? data[18] : "0.0")) ?? 0.0;
}

String getLabelType(String type, Map<String, String> props) {
  final data = type.split(",");
  return data[4];
}

String getLabelText(SimLabel label) {
  return "in,0,0,${label.filterColor.computeLuminance() * 2 - 1},${label.variables["Label Type"]},0,0,0,0,0,0,0,${label.mirrorY},${label.mirrorX},${label.widthScale},${label.blur},${label.fadeOut},${label.fadeOutWhen},${label.fadeOutDuration}";
}

Map<String, String> getLabelProps(SimLabel label) {
  return {
    "id": label.id,
    "name": label.name,
    "x": label.x.toString(),
    "y": label.y.toString(),
    "scaleX": label.width.toString(),
    "scaleY": label.height.toString(),
    "numbers": label.variables["Material Number"] ?? "",
    "color": label.filterColor.value.toString(),
  };
}
