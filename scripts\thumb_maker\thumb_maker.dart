// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:io';
import 'package:image/image.dart';

void main() {
  final spritesDir = Directory("./assets/sprites");
  if (spritesDir.existsSync() == false) {
    print("Folder not exist `./assets/sprites`");
    exit(1);
  }

  Directory('./scripts/thumb_maker/thumbs').createSync();

  final regExp = RegExp("sprites(.+)-frames-high.json");
  final spriteDataFiles = spritesDir.listSync().where((element) {
    return element.path.endsWith('json');
  });

  for (var s in spriteDataFiles) {
    final file = File(s.path).readAsStringSync();
    final Map<String, dynamic> json = jsonDecode(file);
    final spriteName = regExp.firstMatch(s.path);

    print(spriteName!.group(1));

    final image = decodeImage(File("./assets/sprites/${spriteName.group(1)!.substring(1)}-frames-high.png").readAsBytesSync());
    var frames = (json['frames'] as Map<String, dynamic>);
    List<String> keys = frames.keys.toList();
    final takenSprite = (keys.length > 1) ? keys[(keys.length / 2).round()] : keys[0];
    print(takenSprite);
    var frame = frames[takenSprite];
    var frameSize = frames[takenSprite]['frame'];
    // Consider rotation
    int height = frameSize['h'], width = frameSize['w'];
    if (frame['rotated']) {
      height = frameSize['w'];
      width = frameSize['h'];
    }

    var croppedImage = copyCrop(
      image!,
      x: frameSize['x'],
      y: frameSize['y'],
      width: width,
      height: height,
    ); // If rotated, also rotate the image 90 degrees to right
    if (frame['rotated']) {
      croppedImage = copyRotate(croppedImage, angle: -90);
    }
    print('scripts/thumb_maker/thumbs/${spriteName.group(1)!.substring(1)}.png');
    File('./scripts/thumb_maker/thumbs/${spriteName.group(1)!.substring(1)}.png').writeAsBytesSync(encodePng(croppedImage));
    print("====");
  }
}
