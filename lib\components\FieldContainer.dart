import 'package:flutter/material.dart';
import 'package:simsushare_player/utils/constants.dart';

class FieldContainer extends StatelessWidget {
  final Widget? child;
  final EdgeInsets? padding;
  const FieldContainer({Key? key, this.child, this.padding}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(border: Border.all(color: white60), borderRadius: BorderRadius.circular(12)),
      // padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 24),
      padding: padding ?? const EdgeInsets.symmetric(vertical: 10, horizontal: 24),
      child: child,
    );
  }
}
