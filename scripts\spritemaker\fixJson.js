"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var fs = require("fs");
var path = require("path");
var jsonPath = path.join(__dirname, 'output');
var fileName = fs.readdirSync(jsonPath).find(function (file) { return file.endsWith('.json'); });
if (!fileName) {
    throw new Error("No json file found in " + jsonPath);
}
var data = JSON.parse(fs.readFileSync(path.join(jsonPath, fileName), 'utf8'));
var updatedFrames = {};
Object.keys(data.frames).forEach(function (frameKey) {
    var frame = data.frames[frameKey];
    var frameSplit = frameKey.split("/");
    var newFrameKey = frameSplit[0] + "/" + parseInt(frameSplit[1]);
    updatedFrames[newFrameKey] = frame;
});
data.frames = updatedFrames;
fs.writeFileSync(path.join(jsonPath, "fixed_" + fileName), JSON.stringify(data), 'utf8');
