import 'package:simsushare_player/models/SimObjects.dart';

SimTimerType? getTimerTypeFromString(String timerType) {
  switch (timerType) {
    case "timeOfDay":
      return SimTimerType.timeOfDay;
    case "countdown":
      return SimTimerType.countdown;
    case "exercise":
      return SimTimerType.exercise;
    case "location":
      return SimTimerType.location;
    case "state":
      return SimTimerType.state;
    default:
      return null;
  }
}

String getTimerTypeString(SimTimerType timerType) {
  switch (timerType) {
    case SimTimerType.timeOfDay:
      return "timeOfDay";
    case SimTimerType.countdown:
      return "countdown";
    case SimTimerType.exercise:
      return "exercise";
    case SimTimerType.location:
      return "location";
    case SimTimerType.state:
      return "state";
    default:
      return "";
  }
}

SimTimerFormat? getTimerFormatFromString(String timerFormat) {
  switch (timerFormat) {
    case "hourMinuteSeconds":
      return SimTimerFormat.hourMinuteSeconds;
    case "minuteSeconds":
      return SimTimerFormat.minuteSeconds;
    default:
      return null;
  }
}

String getTimerFormatString(SimTimerFormat timerFormat) {
  switch (timerFormat) {
    case SimTimerFormat.hourMinuteSeconds:
      return "hourMinuteSeconds";
    case SimTimerFormat.minuteSeconds:
      return "minuteSeconds";
    default:
      return "";
  }
}

String getTimerTextFromDate(DateTime time, SimTimerFormat format) {
  switch (format) {
    case SimTimerFormat.hourMinuteSeconds:
      return "${time.hour}:${time.minute}:${time.second}";
    case SimTimerFormat.minuteSeconds:
      return "${time.minute}:${time.second}";
    default:
      return "Invalid Format";
  }
}

String secondsToTimerText(int seconds, SimTimerFormat format) {
  switch (format) {
    case SimTimerFormat.hourMinuteSeconds:
      final hours = seconds ~/ 3600;
      final minutes = (seconds % 3600) ~/ 60;
      final secs = seconds % 60;
      return "${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}";
    case SimTimerFormat.minuteSeconds:
      final minutes = seconds ~/ 60;
      final secs = seconds % 60;
      return "${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}";
    default:
      return "Invalid Format";
  }
}
