import 'dart:io';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:multi_window/multi_window.dart';
import 'package:desktop_window/desktop_window.dart';
import 'package:simsushare_player/controllers/ClipboardController.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/controllers/UserController.dart';
// import 'package:simsushare_player/pages/DownloadSimulations.dart';
import 'package:simsushare_player/pages/home_views/home_screen.dart';
// import 'package:simsushare_player/pages/ManageLicense.dart';
// import 'package:simsushare_player/pages/NewScenario.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/NewScenarioWithPlayer.dart';
// import 'package:simsushare_player/pages/PlayerSelectSim.dart';
import 'package:simsushare_player/pages/Preview.dart';
import 'package:simsushare_player/pages/Splash.dart';
// import 'package:simsushare_player/pages/UploadSimulations.dart';

import 'package:simsushare_player/utils/constants.dart';

import 'package:scaled_app/scaled_app.dart';
import 'package:simsushare_player/utils/http_override.dart';
import 'package:window_manager/window_manager.dart';

void main(List<String> args) async {
  if (!kIsWeb) {
    if (Platform.isAndroid || Platform.isIOS) {
      WidgetsFlutterBinding.ensureInitialized();
      if (Platform.isAndroid) overrideHttp();
      SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);
    } else if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      if (Platform.isWindows) overrideHttp();
      ScaledWidgetsFlutterBinding.ensureInitialized(
        scaleFactor: (deviceSize) => min(deviceSize.width / 1200, deviceSize.height / 800),
      );
      await windowManager.ensureInitialized();

      await Future.wait([
        // DesktopWindow.setMinWindowSize(const Size(1200, 800)),
        // DesktopWindow.setMaxWindowSize(Size.infinite),
        // DesktopWindow.setWindowSize(const Size(600, 800)),
        DesktopWindow.setWindowSize(const Size(1200, 800)),
      ]);
      // WindowManager.instance.setAspectRatio(12 / 8);
      MultiWindow.init(args);
    }
  }
  await GetStorage.init();
  Get.put(SimController());
  Get.put(UserController());
  Get.put(ClipboardController());
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'SimsUShare Player',
      theme: ThemeData(
        // colorSchemeSeed: brick,
        scaffoldBackgroundColor: Colors.black,
        inputDecorationTheme: const InputDecorationTheme(
          labelStyle: TextStyle(color: white60),
          hintStyle: TextStyle(color: white60),
          enabledBorder: inputOutlinedBorder,
          focusedBorder: inputOutlinedBorder,
          border: InputBorder.none,
        ),
        canvasColor: mainBackgrounds,
        navigationRailTheme: const NavigationRailThemeData(
          backgroundColor: mainBackgrounds,
          selectedLabelTextStyle: selectedDrawerItemTextStyle,
          unselectedLabelTextStyle: unselectedDrawerItemTextStyle,
          selectedIconTheme: IconThemeData(color: yellow),
          unselectedIconTheme: IconThemeData(color: white60),
        ),
        bottomNavigationBarTheme: const BottomNavigationBarThemeData(
          backgroundColor: mainBackgrounds,
          selectedItemColor: yellow,
          selectedLabelStyle: selectedDrawerItemTextStyle,
          unselectedItemColor: Color.fromARGB(95, 255, 255, 255),
          unselectedLabelStyle: unselectedDrawerItemTextStyle,
          selectedIconTheme: IconThemeData(color: yellow),
          unselectedIconTheme: IconThemeData(color: white60),
        ),
        useMaterial3: false,
        tabBarTheme: const TabBarTheme(
          labelColor: yellow,
          unselectedLabelColor: white60,
          indicatorColor: yellow,
        ),
      ),
      initialRoute: "/",
      getPages: [
        GetPage(name: "/", page: () => Splash()),
        GetPage(name: "/home", page: () => const HomeScreen()),
        // GetPage(name: "/manage-license", page: () => const ManageLicense()),
        // GetPage(name: "/player-select-sim", page: () => PlayerSelectSim()),
        GetPage(name: "/player", page: () => Preview()),
        GetPage(name: "/create", page: () => NewScenarioWithPlayer()),
        // GetPage(name: "/download-sims", page: () => DownloadSimulations()),
        // GetPage(name: "/upload-sims", page: () => UploadSimulations()),
      ],
    );
  }
}
