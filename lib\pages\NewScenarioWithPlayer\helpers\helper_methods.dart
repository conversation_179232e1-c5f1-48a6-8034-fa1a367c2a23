// This function can be exported outside the class scope if we provide it with a sim controller object in the parameters
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:collection/collection.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:simsushare_player/controllers/ClipboardController.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/utils/save.dart';

getHighestPriorityObject() {
  final _simController = Get.find<SimController>();
  final locObjects = _simController.getAllLocationObjects(_simController.currentLocation.value);
  if (locObjects.isEmpty) return SimObject(x: 0, y: 0, priority: 0);

  return locObjects.reduce((value, element) => value.priority > element.priority ? value : element);
}

List<Widget> getAllMaskableObjects() {
  final _simController = Get.find<SimController>();
  return [
    ..._simController.currentSim.value!.locations[_simController.currentLocation.value].sprites
        // .where((sprite) => sprite.maskIds
        //     .contains(sim.masks[_simController.selectedSimObjectIndex.value].id))
        .mapIndexed((index, sprite) {
      final mask = _simController.currentSim.value!.masks
          .where((mask) => mask.locationId == _simController.currentSim.value!.locations[_simController.currentLocation.value].id)
          .toList()[_simController.selectedSimObjectIndex.value];
      return Row(
        children: [
          Checkbox(
            checkColor: Colors.white,
            side: const BorderSide(color: Colors.white),
            // value: sprite.maskIds.contains(mask.id),
            value: _simController.currentSim.value!.locations[_simController.currentLocation.value].sprites[index].maskIds.contains(mask.id),
            onChanged: ((checked) {
              if (checked ?? false) {
                _simController.currentSim.value!.locations[_simController.currentLocation.value].sprites[index].maskIds.add(mask.id);
              } else {
                _simController.currentSim.value!.locations[_simController.currentLocation.value].sprites[index].maskIds.remove(mask.id);
              }
              print("Sprite: ${sprite.name} now has masks: ${sprite.maskIds}");
              _simController.currentSim.refresh();
            }),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              // sprite.id,
              sprite.name,
              style: const TextStyle(color: Colors.white),
            ),
          )
        ],
      );
    }).toList(),
    ..._simController.currentSim.value!.locations[_simController.currentLocation.value].images
        // .where((sprite) => sprite.maskIds
        //     .contains(sim.masks[_simController.selectedSimObjectIndex.value].id))
        .mapIndexed((index, image) {
      final mask = _simController.currentSim.value!.masks
          .where((mask) => mask.locationId == _simController.currentSim.value!.locations[_simController.currentLocation.value].id)
          .toList()[_simController.selectedSimObjectIndex.value];
      return Row(
        children: [
          Checkbox(
            checkColor: Colors.white,
            side: const BorderSide(color: Colors.white),
            // value: image.maskIds.contains(mask.id),
            value: _simController.currentSim.value!.locations[_simController.currentLocation.value].images[index].maskIds.contains(mask.id),
            onChanged: ((checked) {
              if (checked ?? false) {
                _simController.currentSim.value!.locations[_simController.currentLocation.value].images[index].maskIds.add(mask.id);
              } else {
                _simController.currentSim.value!.locations[_simController.currentLocation.value].images[index].maskIds.remove(mask.id);
              }
              print("Image: ${image.id} now has masks: ${image.maskIds}");
              _simController.currentSim.refresh();
            }),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              // image.id,
              image.id,
              style: const TextStyle(color: Colors.white),
            ),
          )
        ],
      );
    }),
    ..._simController.currentSim.value!.locations[_simController.currentLocation.value].shapes
        // .where((sprite) => sprite.maskIds
        //     .contains(sim.masks[_simController.selectedSimObjectIndex.value].id))
        .mapIndexed((index, shape) {
      final mask = _simController.currentSim.value!.masks
          .where((mask) => mask.locationId == _simController.currentSim.value!.locations[_simController.currentLocation.value].id)
          .toList()[_simController.selectedSimObjectIndex.value];
      return Row(
        children: [
          Checkbox(
            checkColor: Colors.white,
            side: const BorderSide(color: Colors.white),
            // value: shape.maskIds.contains(mask.id),
            value: _simController.currentSim.value!.locations[_simController.currentLocation.value].shapes[index].maskIds.contains(mask.id),
            onChanged: ((checked) {
              if (checked ?? false) {
                _simController.currentSim.value!.locations[_simController.currentLocation.value].shapes[index].maskIds.add(mask.id);
              } else {
                _simController.currentSim.value!.locations[_simController.currentLocation.value].shapes[index].maskIds.remove(mask.id);
              }
              print("Shape: ${shape.id} now has masks: ${shape.maskIds}");
              _simController.currentSim.refresh();
            }),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              // shape.id,
              shape.id,
              style: const TextStyle(color: Colors.white),
            ),
          )
        ],
      );
    }),
    ..._simController.currentSim.value!.locations[_simController.currentLocation.value].labels
        // .where((sprite) => sprite.maskIds
        //     .contains(sim.masks[_simController.selectedSimObjectIndex.value].id))
        .mapIndexed((index, label) {
      final mask = _simController.currentSim.value!.masks
          .where((mask) => mask.locationId == _simController.currentSim.value!.locations[_simController.currentLocation.value].id)
          .toList()[_simController.selectedSimObjectIndex.value];
      return Row(
        children: [
          Checkbox(
            checkColor: Colors.white,
            side: const BorderSide(color: Colors.white),
            // value: shape.maskIds.contains(mask.id),
            value: _simController.currentSim.value!.locations[_simController.currentLocation.value].labels[index].maskIds.contains(mask.id),
            onChanged: ((checked) {
              if (checked ?? false) {
                _simController.currentSim.value!.locations[_simController.currentLocation.value].labels[index].maskIds.add(mask.id);
              } else {
                _simController.currentSim.value!.locations[_simController.currentLocation.value].labels[index].maskIds.remove(mask.id);
              }
              print("Shape: ${label.id} now has masks: ${label.maskIds}");
              _simController.currentSim.refresh();
            }),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              // shape.id,
              label.name,
              style: const TextStyle(color: Colors.white),
            ),
          )
        ],
      );
    }),
    ..._simController.currentSim.value!.locations[_simController.currentLocation.value].containers
        // .where((sprite) => sprite.maskIds
        //     .contains(sim.masks[_simController.selectedSimObjectIndex.value].id))
        .mapIndexed((index, container) {
      final mask = _simController.currentSim.value!.masks
          .where((mask) => mask.locationId == _simController.currentSim.value!.locations[_simController.currentLocation.value].id)
          .toList()[_simController.selectedSimObjectIndex.value];
      return Row(
        children: [
          Checkbox(
            checkColor: Colors.white,
            side: const BorderSide(color: Colors.white),
            // value: shape.maskIds.contains(mask.id),
            value: _simController.currentSim.value!.locations[_simController.currentLocation.value].containers[index].maskIds.contains(mask.id),
            onChanged: ((checked) {
              if (checked ?? false) {
                _simController.currentSim.value!.locations[_simController.currentLocation.value].containers[index].maskIds.add(mask.id);
              } else {
                _simController.currentSim.value!.locations[_simController.currentLocation.value].containers[index].maskIds.remove(mask.id);
              }
              print("Shape: ${container.id} now has masks: ${container.maskIds}");
              _simController.currentSim.refresh();
            }),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              // shape.id,
              container.name,
              style: const TextStyle(color: Colors.white),
            ),
          )
        ],
      );
    }),
    ..._simController.currentSim.value!.locations[_simController.currentLocation.value].people
        // .where((sprite) => sprite.maskIds
        //     .contains(sim.masks[_simController.selectedSimObjectIndex.value].id))
        .mapIndexed((index, person) {
      final mask = _simController.currentSim.value!.masks
          .where((mask) => mask.locationId == _simController.currentSim.value!.locations[_simController.currentLocation.value].id)
          .toList()[_simController.selectedSimObjectIndex.value];
      return Row(
        children: [
          Checkbox(
            checkColor: Colors.white,
            side: const BorderSide(color: Colors.white),
            // value: shape.maskIds.contains(mask.id),
            value: _simController.currentSim.value!.locations[_simController.currentLocation.value].people[index].maskIds.contains(mask.id),
            onChanged: ((checked) {
              if (checked ?? false) {
                _simController.currentSim.value!.locations[_simController.currentLocation.value].people[index].maskIds.add(mask.id);
              } else {
                _simController.currentSim.value!.locations[_simController.currentLocation.value].people[index].maskIds.remove(mask.id);
              }
              print("Shape: ${person.id} now has masks: ${person.maskIds}");
              _simController.currentSim.refresh();
            }),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              // shape.id,
              person.name,
              style: const TextStyle(color: Colors.white),
            ),
          )
        ],
      );
    })
  ];
}

deleteSelectedObject() {
  final _simController = Get.find<SimController>();
  final _clipboardController = Get.find<ClipboardController>();
  switch (_simController.selectedType.value) {
    case SimObjectType.sprite:
      _simController.currentSim.value!.locations[_simController.currentLocation.value].sprites.removeAt(_simController.selectedSimObjectIndex.value);
      break;
    case SimObjectType.text:
      _simController.currentSim.value!.locations[_simController.currentLocation.value].texts.removeAt(_simController.selectedSimObjectIndex.value);
      break;
    case SimObjectType.image:
      for (var loc in _simController.currentSim.value!.locations) {
        final target =
            _simController.currentSim.value!.locations[_simController.currentLocation.value].images[_simController.selectedSimObjectIndex.value].id +
                "___" +
                loc.id;
        for (var person in loc.people) {
          if (person.syncVariable == target) {
            person.syncVariable = null;
          }
        }
        for (var img in loc.images) {
          if (img.syncVariable == target) {
            img.syncVariable = null;
          }
        }
      }
      _simController.currentSim.value!.locations[_simController.currentLocation.value].images.removeAt(_simController.selectedSimObjectIndex.value);
      break;
    case SimObjectType.audio:
      Future.sync(() async {
        final audio =
            _simController.currentSim.value!.locations[_simController.currentLocation.value].sounds[_simController.selectedSimObjectIndex.value];
        if (audio.path.isNotEmpty) {
          await File(audio.path).delete();
        }
      }).catchError((e) {
        print("Error deleting audio: $e");
      });
      _simController.currentSim.value!.locations[_simController.currentLocation.value].sounds.removeAt(_simController.selectedSimObjectIndex.value);
      break;
    case SimObjectType.shape:
      _simController.currentSim.value!.locations[_simController.currentLocation.value].shapes.removeAt(_simController.selectedSimObjectIndex.value);
      break;
    case SimObjectType.locationJumper:
      _simController.currentSim.value!.locations[_simController.currentLocation.value].jumpers.removeAt(_simController.selectedSimObjectIndex.value);
      break;
    case SimObjectType.container:
      _simController.currentSim.value!.locations[_simController.currentLocation.value].containers
          .removeAt(_simController.selectedSimObjectIndex.value);
      break;
    case SimObjectType.person:
      for (var loc in _simController.currentSim.value!.locations) {
        final target =
            _simController.currentSim.value!.locations[_simController.currentLocation.value].people[_simController.selectedSimObjectIndex.value].id;
        for (var person in loc.people) {
          if (person.syncVariable == target) {
            person.syncVariable = null;
          }
        }
        for (var img in loc.images) {
          if (img.syncVariable == target) {
            img.syncVariable = null;
          }
        }
      }
      _simController.currentSim.value!.locations[_simController.currentLocation.value].people.removeAt(_simController.selectedSimObjectIndex.value);
      break;
    case SimObjectType.label:
      _simController.currentSim.value!.locations[_simController.currentLocation.value].labels.removeAt(_simController.selectedSimObjectIndex.value);
      break;
    case SimObjectType.timer:
      _simController.currentSim.value!.locations[_simController.currentLocation.value].timers.removeAt(_simController.selectedSimObjectIndex.value);
      break;
    case SimObjectType.mask:
      // NOTE: we might need to remove the mask from all sim objects
      // final maskId = _simController.currentSim.value!.masks[_simController.selectedSimObjectIndex.value].id;
      // _simController.currentSim.value!.locations[_simController.currentLocation.value].sprites
      //     .forEach((s) => s.maskIds.remove(maskId));
      final targetMask = _simController.currentSim.value!.masks
          .where((mask) => mask.locationId == _simController.currentSim.value!.locations[_simController.currentLocation.value].id)
          .toList()[_simController.selectedSimObjectIndex.value];
      _simController.currentSim.value!.masks.removeWhere((mask) => mask.id == targetMask.id);
      break;
    default:
      print("Invalid Sim Selected Type: ${_simController.selectedType.value}");
  }
  _simController.selectedSimObjectIndex.value = -1;
  _simController.selectedType.value = null;
  _simController.currentSim.refresh();
  _clipboardController.clearSelection();
}

copyToClipboard({bool all = false}) {
  final clipboard = Get.find<ClipboardController>();
  final _simController = Get.find<SimController>();
  if (all) {
    clipboard.simObjects.addAll([
      ..._simController.currentSim.value!.locations[_simController.currentLocation.value].sprites,
      ..._simController.currentSim.value!.locations[_simController.currentLocation.value].images,
      ..._simController.currentSim.value!.locations[_simController.currentLocation.value].shapes,
      ..._simController.currentSim.value!.locations[_simController.currentLocation.value].jumpers,
      ..._simController.currentSim.value!.locations[_simController.currentLocation.value].texts,
      ..._simController.currentSim.value!.locations[_simController.currentLocation.value].sounds,
      ..._simController.currentSim.value!.locations[_simController.currentLocation.value].labels,
      ..._simController.currentSim.value!.locations[_simController.currentLocation.value].containers,
      ..._simController.currentSim.value!.locations[_simController.currentLocation.value].people,
    ]);
    clipboard.masks.value = _simController.currentSim.value!.masks
        .where((mask) => mask.locationId == _simController.currentSim.value!.locations[_simController.currentLocation.value].id)
        .where((mask) => !clipboard.masks.any((cMask) => cMask.id == mask.id))
        .toList();
  } else {
    if (_simController.selectedType.value == SimObjectType.mask) {
      final mask = _simController.getCurrentSelectedMask();
      print("Mask to copy: $mask");
      if (clipboard.masks.any((cMask) => cMask.id == mask.id)) {
        print("Mask with id ${mask.id} already in clipboard");
        return;
      }
      final maskCopy = mask.copy();
      maskCopy.locationId = "";
      clipboard.masks.add(maskCopy);
      return;
    }
    final simObj = _simController.getCurrentSelectedObject();
    if (simObj == null) {
      print("Nothing to copy to clipboard");
      return;
    }
    if (clipboard.simObjects.firstWhereOrNull((element) => element.id == simObj.id) != null) {
      print("Object with id ${simObj.id} already in clipboard");
      return;
    }
    clipboard.simObjects.add(simObj);
  }
  clipboard.simObjects.value = clipboard.simObjects.toSet().toList();
}

Future<Directory> getSimSaveDirectory(Scenario sim, {bool deep = false}) async {
  // NOTE: consider throwing an error if using web
  final dir = Directory(
    sim.directoryPath.isNotEmpty ? sim.directoryPath : (await getApplicationDocumentsDirectory()).path + "/simulations/" + sim.name,
  );
  if (deep) {
    return Directory((await dir.list().toList()).firstWhere((entry) => entry is Directory).path);
  } else {
    return dir;
  }
}

saveSim({bool showNotification = true, Scenario? simulation}) async {
  if (kIsWeb) {
    print("Saving not supported on web");
    if (showNotification) {
      Get.snackbar(
        "Saved",
        "Successfully saved simulation",
        duration: const Duration(seconds: 3),
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
    return;
  }
  final _simController = Get.find<SimController>();
  final sim = simulation ?? _simController.currentSim.value!;
  // final simDef = generateSimDef(simValue);
  // print(simDef);
  Directory originalDir = await getSimSaveDirectory(sim);
  Directory originalSubDir;
  if (sim.directoryPath.isEmpty) {
    final pathName = originalDir.path + "/${sim.id}";
    if (pathName.contains("//__")) {
      print("Path name contains //__ and attempting a wrongful creation of a directory during auto-save on");
      return;
    }
    // await originalDir.create(recursive: true);
    // originalSubDir = Directory(originalDir.path + "/${sim.id}");
    originalSubDir = Directory(pathName);
    await originalSubDir.create(recursive: true);
  } else {
    originalSubDir = Directory(originalDir.listSync().firstWhere((element) => element is Directory).path);
  }
  await savetoDir(originalSubDir, _simController.currentSim.value!);
  _simController.saved.value = true;
  _simController.signalStream.add("saved");
  if (showNotification) {
    Get.snackbar(
      "Saved",
      "Successfully saved simulation",
      duration: const Duration(seconds: 3),
      colorText: Colors.white,
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
