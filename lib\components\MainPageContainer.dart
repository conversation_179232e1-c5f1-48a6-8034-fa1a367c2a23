import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/components/OptionsButton.dart';

class MainPageContainer extends StatelessWidget {
  final Widget child;
  final String title;
  final List<Widget>? options;
  const MainPageContainer({Key? key, required this.title, required this.child, this.options}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Column(
            children: [
              Expanded(
                child: Container(
                  color: Colors.grey.shade300,
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ElevatedButton(
                              onPressed: () {
                                Get.back();
                              },
                              child: const Text("Back"),
                            ),
                          ],
                        ),
                        flex: 1,
                      ),
                      Expanded(
                        child: Text(
                          title,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 28,
                          ),
                        ),
                        flex: 7,
                      ),
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: (options != null) ? [OptionsButton(onTap: () {})] : [],
                        ),
                        flex: 1,
                      ),
                    ],
                  ),
                ),
                flex: 1,
              ),
              Expanded(
                child: child,
                flex: 9,
              ),
            ],
          )
        ],
      ),
    );
  }
}
