import 'dart:io';

import 'package:file_picker/file_picker.dart' show PlatformFile;
import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import 'package:get/get.dart';
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/pages/home_views/library_view/library_view.dart';
import 'package:simsushare_player/utils/picker.dart';

// NOTE: NOT USED
class UploadFileDialog extends StatelessWidget {
  final Directory directory;

  UploadFileDialog({
    Key? key,
    required this.directory,
  }) : super(key: key);

  final RxList<PlatformFile> files = RxList<PlatformFile>.empty(growable: true);
  final uploading = false.obs;

  @override
  Widget build(BuildContext context) {
    final _libraryController = Get.put(LibraryController());
    return ContentDialog(
      title: "Upload File",
      content: Container(
        constraints: const BoxConstraints(minHeight: 150),
        child: SingleChildScrollView(
          child: Obx(
            () => Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ...files.map(
                  (f) => Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Image.file(
                        File(f.path!),
                        width: 70,
                        height: 70,
                      ),
                      const SizedBox(width: 14),
                      Expanded(
                        child: Text(
                          path.basename(f.path!),
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                      IconButton(
                        onPressed: () {},
                        icon: const Icon(
                          Icons.delete,
                          color: Colors.white,
                        ),
                      )
                    ],
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    TransparentButton(
                      onPressed: () async {
                        final pickerFiles = await pickFiles(
                          allowMultiple: true,
                          // withData: true,
                          allowedExtensions: ["jpg", "jpeg", "png", "gif", "mp3", "wav"],
                        );
                        if (pickerFiles == null) return;
                        files.addAll(pickerFiles.files);
                        files.refresh();
                      },
                      label: "Add Files",
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
      actions: [
        OrangeButton(
          onPressed: () async {
            if (files.isEmpty) return;
            uploading.value = true;
            await Future.wait(files.map((file) => File(directory.path + "/" + file.name).writeAsBytes(file.bytes!)));
            // final newFiles = <File>[].obs;
            // await Future.wait(newfiles.map((file) async {
            //   String fileName = file.name;
            //   String fileNameWithoutExt = path.basenameWithoutExtension(fileName);
            //   String ext = path.extension(fileName);
            //   int suffix = 1;

            //   while (await File('${folder.path}/$fileName').exists()) {
            //     fileName = '$fileNameWithoutExt($suffix)$ext';
            //     suffix++;
            //   }
            //   // final newFile = await File(file.path!).copy(folder.path + "/" + file.name);
            //   final newFile = await File(file.path!).copy(folder.path + "/" + fileName);
            //   newFiles.add(newFile);
            //   // return newFiles;
            //   return newFile;
            // }));

            // // final uniqueFilesList = await addFilesWithUniqueNames(folder.path, newfiles);

            // // controller.libraryFolders[folderIndex].files.addAll(uniqueFilesList);
            // _libraryController.libraryFolders[folderIndex].files.addAll(newFiles);
            // _libraryController.libraryFolders[folderIndex].files.refresh();

            uploading.value = false;
            Get.back(canPop: false, result: true);
          },
          label: "Upload",
          disabled: uploading.value,
        ),
      ],
    );
  }
}
