import 'dart:math';
import 'dart:ui';

// import 'package:flutter/material.dart';
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/models/Simulation.dart';

double calculateScaleFromImage({
  required Scenario sim,
  required Image img,
}) {
  final widthScale = sim.width / img.width;
  final heightScale = sim.height / img.height;
  return max(widthScale, heightScale);
}

SimulationLocation makeLocationFromImage({
  required Scenario sim,
  required Image img,
  required String imgPath,
  required String name,
  String id = "",
  String state = "",
  List<SimSprite> sprites = const [],
}) {
  return SimulationLocation(
    id: id,
    name: name,
    state: state,
    sprites: sprites,
    image: imgPath,
    imageScale: calculateScaleFromImage(sim: sim, img: img),
  );
}
