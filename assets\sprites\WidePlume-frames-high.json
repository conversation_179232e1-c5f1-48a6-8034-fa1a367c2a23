{"frames": {"smoke_plume_2_wide_01": {"frame": {"x": 0, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_02": {"frame": {"x": 317, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_03": {"frame": {"x": 634, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_04": {"frame": {"x": 951, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_05": {"frame": {"x": 1268, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_06": {"frame": {"x": 1585, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_07": {"frame": {"x": 1902, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_08": {"frame": {"x": 2219, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_09": {"frame": {"x": 2536, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_10": {"frame": {"x": 2853, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_11": {"frame": {"x": 3170, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_12": {"frame": {"x": 3487, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_13": {"frame": {"x": 3804, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_14": {"frame": {"x": 4121, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_15": {"frame": {"x": 4438, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_16": {"frame": {"x": 4755, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_17": {"frame": {"x": 5072, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_18": {"frame": {"x": 5389, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_19": {"frame": {"x": 5706, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_20": {"frame": {"x": 6023, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_21": {"frame": {"x": 6340, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_22": {"frame": {"x": 6657, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_23": {"frame": {"x": 6974, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_24": {"frame": {"x": 7291, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_25": {"frame": {"x": 7608, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_26": {"frame": {"x": 7925, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_27": {"frame": {"x": 8242, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_28": {"frame": {"x": 8559, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_29": {"frame": {"x": 8876, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}, "smoke_plume_2_wide_30": {"frame": {"x": 9193, "y": 0, "w": 317, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 317, "h": 431}, "sourceSize": {"w": 317, "h": 431}}}, "meta": {"app": "https://www.codeandweb.com/texturepacker", "version": "1.0", "image": "Output.png", "format": "RGBA8888", "size": {"w": 9510, "h": 431}, "scale": "1.25", "smartupdate": "$TexturePacker:SmartUpdate:9eb3e95157fe789ae9ee78a20d4de357:f2c050cff1227535707934e88a9bc340:ab242ca87e7871abc98cd1fb098a82c9$"}}