import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/utils/constants.dart';

class ContentDialog extends StatelessWidget {
  final String title;
  final Widget content;
  final List<Widget> actions;
  final double? width;
  final double? height;
  final bool isAddMapDialog;
  final int? triggerOnEnterKeyActionIndex;
  final bool disableCloseButton;

  const ContentDialog({
    Key? key,
    required this.title,
    required this.content,
    required this.actions,
    this.width,
    this.height,
    this.isAddMapDialog = false,
    this.triggerOnEnterKeyActionIndex,
    this.disableCloseButton = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final focusNode = FocusNode();
    return Dialog(
      backgroundColor: sidebarDark,
      insetPadding: isAddMapDialog
          ? EdgeInsets.zero
          : EdgeInsets.symmetric(
              horizontal: isMobileScreen ? 10 : 40.0,
              vertical: isMobileScreen ? 5 : 24.0,
            ),
      child: RawKeyboardListener(
        onKey: (value) {
          if (value.isKeyPressed(LogicalKeyboardKey.enter)) {
            if (triggerOnEnterKeyActionIndex != null) {
              (actions[triggerOnEnterKeyActionIndex!] as MaterialButton).onPressed!();
            }
          }
        },
        focusNode: focusNode,
        child: SizedBox(
          width: width,
          height: height,
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 24),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(child: Text(title, style: const TextStyle(color: Colors.white, fontSize: 18))),
                    if (!disableCloseButton)
                      IconButton(
                        onPressed: () {
                          // Get.back(closeOverlays: true);
                          Get.back();
                        },
                        icon: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 14,
                        ),
                      )
                  ],
                ),
              ),
              const Divider(color: Colors.white, thickness: 2),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    vertical: 20,
                    horizontal: 24,
                  ),
                  child: content,
                ),
              ),
              const Divider(color: Colors.white, thickness: 2),
              Container(
                padding: const EdgeInsets.symmetric(vertical: 18),
                child: Row(
                  children: actions,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
