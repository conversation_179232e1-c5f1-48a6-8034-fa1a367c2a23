import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:ui' as ui;
import 'package:collection/collection.dart';

import 'package:flame/extensions.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/components/PickLocationImage.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/sliders/custom_slider.dart';
import 'package:simsushare_player/utils/constants.dart';
import 'package:simsushare_player/utils/parsers.dart';

class AddMapDialog extends StatelessWidget {
  final Scenario scenario;
  final Directory directory;

  AddMapDialog({
    Key? key,
    required this.scenario,
    required this.directory,
  }) : super(key: key);

  final rotation = 0.0.obs;
  final scale = 1.0.obs;
  final brighteness = 0.0.obs;
  final offset = Offset.zero.obs;
  final globalKey = GlobalKey();

  /* _onCancel() {
    final controller = Get.find<AddMapController>();
    controller.isEdit.value = false;
    controller._parsedScenario.value = null;
    controller.locations.value = [];
    controller.storedImageBytes.value = null;
    controller.storedImagePath.value = "";
    controller.selectedImagePath.value = "";
    controller.jsonFileExists.value = false;
  } */

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final controller = Get.find<AddMapController>();

      /// Capture updated image after scaling and rotating
      Future<ui.Image> captureUpdatedImage() async {
        RenderRepaintBoundary boundary = globalKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
        return await boundary.toImage(pixelRatio: 1.0);
      }

      /// Convert image to byte data
      Future<Uint8List> convertUpdatedImageToByteData(ui.Image image) async {
        ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
        return byteData!.buffer.asUint8List();
      }

      /// Save updated image in the same directory
      Future<File> saveUpdatedLocationImage(Uint8List bytes, String path) async {
        controller.isImageUpdated.value = true;
        controller.selectedImagePath.value = '$path/simMap.png';
        File file = File(controller.selectedImagePath.value);
        await file.writeAsBytes(bytes);

        // Evict the image file from cache
        final ImageCache? imageCache = PaintingBinding.instance.imageCache;
        imageCache!.evict(FileImage(file));

        return file;
      }

      return controller.isEdit.value
          ? ContentDialog(
              title: 'Update Map',
              isAddMapDialog: true,
              content: NotificationListener<ScrollNotification>(
                onNotification: isMobileScreen
                    ? (notification) {
                        if (notification is ScrollEndNotification) {
                          if (controller.jsonFileExists.value) {
                            controller.storedImageHeightOffset.value = 70 - notification.metrics.pixels;
                          } else {
                            if (controller.selectedImagePath.value.isNotEmpty) {
                              controller.selectedImageHeightOffset.value = 160 - notification.metrics.pixels;
                            }
                          }
                        }
                        return true;
                      }
                    : (notification) {
                        if (notification is ScrollEndNotification) {
                          if (controller.jsonFileExists.value) {
                            controller.storedImageHeightOffset.value = 115 - notification.metrics.pixels;
                          } else {
                            if (controller.selectedImagePath.value.isNotEmpty) {
                              controller.selectedImageHeightOffset.value = 190 - notification.metrics.pixels;
                            }
                          }
                        }
                        return true;
                      },
                child: Column(
                  children: [
                    // Image
                    Obx(() {
                      return Center(
                        child: SizedBox(
                          height: 640 / 2,
                          width: 940 / 2,
                          child: ClipRRect(
                            clipBehavior: Clip.hardEdge,
                            child: RepaintBoundary(
                              key: globalKey,
                              child: Transform.scale(
                                scale: scale.value,
                                origin: offset.value,
                                child: Transform.rotate(
                                  angle: rotation.value * pi / 180,
                                  child: ColorFiltered(
                                    colorFilter: ColorFilter.matrix(
                                      ColorFilterGenerator.brightnessAdjustMatrix(value: brighteness.value),
                                    ),
                                    child: GestureDetector(
                                      onPanUpdate: (details) {
                                        offset.value -= Offset(details.delta.dx, details.delta.dy);
                                      },
                                      child: controller.storedImagePath.isNotEmpty
                                          ? Image.file(
                                              File(controller.storedImagePath.value),
                                              fit: BoxFit.contain,
                                              alignment: Alignment.topCenter,
                                              height: 640 / 2,
                                              width: 940 / 2,
                                            )
                                          : Image.file(
                                              File(controller.selectedImagePath.value),
                                              fit: BoxFit.contain,
                                              alignment: Alignment.topCenter,
                                              height: 640 / 2,
                                              width: 940 / 2,
                                            ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    }),
                    const SizedBox(height: 10),
                    // Rotate slider
                    Obx(() {
                      return Center(
                        child: SizedBox(
                          width: Get.width * 0.7,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                "Rotation",
                                style: TextStyle(color: Colors.white),
                              ),
                              SizedBox(
                                width: Get.width * 0.6,
                                child: SliderTheme(
                                  data: SliderThemeData(
                                    thumbShape: CustomSliderThumbShape(
                                      thumbRadius: 18.0,
                                      sliderValue: rotation.value.toPrecision(2),
                                      thumbColor: brick,
                                      textColor: Colors.white,
                                    ),
                                  ),
                                  child: Slider(
                                    value: rotation.value,
                                    onChanged: (value) {
                                      rotation.value = value;
                                    },
                                    min: -180,
                                    max: 180,
                                    divisions: 360,
                                    activeColor: brick,
                                    inactiveColor: brick,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }),
                    const SizedBox(height: 10),
                    // Scale slider
                    Obx(() {
                      return Center(
                        child: SizedBox(
                          width: Get.width * 0.7,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                "Scale",
                                style: TextStyle(color: Colors.white),
                              ),
                              SizedBox(
                                width: Get.width * 0.6,
                                child: SliderTheme(
                                  data: SliderThemeData(
                                    thumbShape: CustomSliderThumbShape(
                                      thumbRadius: 18.0,
                                      sliderValue: scale.value.toPrecision(2),
                                      thumbColor: brick,
                                      textColor: Colors.white,
                                    ),
                                  ),
                                  child: Slider(
                                    value: scale.value,
                                    onChanged: (value) {
                                      scale.value = value;
                                      if (value == 1) {
                                        offset.value = Offset.zero;
                                      }
                                    },
                                    min: 0.1,
                                    max: 5,
                                    divisions: 40,
                                    activeColor: brick,
                                    inactiveColor: brick.withOpacity(0.7),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }),
                    const SizedBox(height: 10),
                    // Brightness slider
                    Obx(() {
                      return Center(
                        child: SizedBox(
                          width: Get.width * 0.7,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                "Brightness",
                                style: TextStyle(color: Colors.white),
                              ),
                              SizedBox(
                                width: Get.width * 0.6,
                                child: SliderTheme(
                                  data: SliderThemeData(
                                    thumbShape: CustomSliderThumbShape(
                                      thumbRadius: 18.0,
                                      sliderValue: brighteness.value.toPrecision(2),
                                      thumbColor: brick,
                                      textColor: Colors.white,
                                    ),
                                  ),
                                  child: Slider(
                                    value: brighteness.value,
                                    onChanged: (value) {
                                      brighteness.value = value;
                                    },
                                    min: -1.0,
                                    max: 1.0,
                                    divisions: 40,
                                    activeColor: brick,
                                    inactiveColor: brick.withOpacity(0.7),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }),
                  ],
                ),
              ),
              actions: [
                  TransparentButton(
                    label: "Cancel",
                    onPressed: () {
                      controller.isEdit.value = false;
                    },
                  ),
                  OrangeButton(
                    label: "Update",
                    onPressed: () {
                      captureUpdatedImage().then((image) {
                        convertUpdatedImageToByteData(image).then((bytes) {
                          saveUpdatedLocationImage(bytes, directory.path).then((value) => controller.isEdit.value = false);
                        });
                      });
                    },
                  ),
                ])
          : ContentDialog(
              title: controller.jsonFileExists.value ? 'Edit Map' : 'Add Map',
              isAddMapDialog: true,
              content: NotificationListener<ScrollNotification>(
                onNotification: isMobileScreen
                    ? (notification) {
                        if (notification is ScrollEndNotification) {
                          if (controller.jsonFileExists.value) {
                            controller.storedImageHeightOffset.value = 70 - notification.metrics.pixels;
                          } else {
                            if (controller.selectedImagePath.value.isNotEmpty) {
                              controller.selectedImageHeightOffset.value = 160 - notification.metrics.pixels;
                            }
                          }
                        }
                        return true;
                      }
                    : (notification) {
                        if (notification is ScrollEndNotification) {
                          if (controller.jsonFileExists.value) {
                            controller.storedImageHeightOffset.value = 115 - notification.metrics.pixels;
                          } else {
                            if (controller.selectedImagePath.value.isNotEmpty) {
                              controller.selectedImageHeightOffset.value = 190 - notification.metrics.pixels;
                            }
                          }
                        }
                        return true;
                      },
                child: AddMapBody(
                  controller: controller,
                ),
              ),
              actions: [
                  // Cancel Button
                  TransparentButton(
                    label: "Cancel",
                    onPressed: () {
                      // Clear locations values
                      controller.locations.clear();

                      // Clear selected image values - first time image
                      controller.selectedImagePath.value = "";
                      controller.selectedImageHeightOffset.value = 190;

                      // Clear stored image values
                      controller.storedImageBytes.value = null;
                      controller.storedImagePath.value = "";
                      controller.storedImageHeightOffset.value = 115;

                      controller.isEdit.value = false;
                      Get.back();
                    },
                  ),

                  // Delete Button
                  controller.jsonFileExists.value
                      ? Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: TextButton(
                            onPressed: () => controller.deleteMap(directory.path),
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 14.5),
                              child: const Text("Delete", style: TextStyle(color: Colors.white, fontSize: 16)),
                            ),
                            style: TextButton.styleFrom(
                              backgroundColor: Colors.red,
                              shape: const StadiumBorder(),
                            ),
                          ),
                        )
                      : const SizedBox(),

                  /* // Edit Button
                  controller.selectedImagePath.isNotEmpty || controller.storedImagePath.isNotEmpty
                      ? Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: TextButton(
                            onPressed: () => controller.isEdit.toggle(),
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 14.5),
                              child: const Text("Edit", style: TextStyle(color: Colors.white, fontSize: 16)),
                            ),
                            style: TextButton.styleFrom(
                              backgroundColor: Colors.orange,
                              shape: const StadiumBorder(),
                            ),
                          ),
                        )
                      : const SizedBox(), */

                  // Save Button
                  controller.selectedImagePath.isNotEmpty || controller.storedImagePath.isNotEmpty
                      ? OrangeButton(
                          label: "Save",
                          onPressed: () => controller.saveLocations(directory.path, controller.selectedImagePath.value),
                        )
                      : const SizedBox(),
                ]);
    });
  }
}

const addMapConatinerWidth = 400.0;
const addMapConatinerHeight = 400.0;

class AddMapBody extends StatelessWidget {
  const AddMapBody({
    Key? key,
    required this.controller,
  }) : super(key: key);

  final AddMapController controller;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // In "Add map" state, if the user selected an image, then update image height value using updateSelectedImageHeightValue
      if (controller.jsonFileExists.isFalse && controller.selectedImagePath.isNotEmpty) controller.updateSelectedImageHeightValue(context);
      // print("Rerendering AddMapBody");
      return SingleChildScrollView(
        child: Column(
          children: [
            if (controller.jsonFileExists.isFalse) ...[
              const Text(
                "Upload Image",
                style: TextStyle(color: Colors.white),
              ),
              const SizedBox(height: 11),
              PickLocationImage(
                multi: false,
                onImageSelected: controller.selectImage,
              ),
              const SizedBox(height: 10),
            ],
            // In "Edit map" & "Add map" states, check that image path is not empty
            controller.jsonFileExists.isTrue && controller.storedImagePath.value.isEmpty ||
                    controller.jsonFileExists.isFalse && controller.selectedImagePath.isEmpty
                ? const SizedBox()
                : Flex(
                    direction: isMobile ? Axis.vertical : Axis.horizontal,
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Container(
                        width: addMapConatinerWidth,
                        height: addMapConatinerHeight,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.white, width: 1),
                        ),
                        child: controller.locations.isNotEmpty // NOTE: needs to be here to maintain reactivity
                            ? Builder(builder: (context) {
                                return Stack(
                                  fit: StackFit.expand,
                                  children: [
                                    ScrollConfiguration(
                                      behavior: const ScrollBehavior().copyWith(overscroll: false),
                                      child: DragTarget(
                                        builder: (context, candidateData, rejectedData) {
                                          return SizedBox(
                                            width: addMapConatinerWidth,
                                            height: addMapConatinerHeight,
                                            child: ClipRRect(
                                              clipBehavior: Clip.hardEdge,
                                              child: Transform.scale(
                                                scale: controller.scale.value,
                                                origin: controller.offset.value,
                                                child: Transform.rotate(
                                                  angle: controller.rotation.value * pi / 180,
                                                  child: GestureDetector(
                                                    onPanUpdate: (details) {
                                                      controller.offset.value -= Offset(details.delta.dx, details.delta.dy);
                                                      controller.locations.refresh();
                                                    },
                                                    child: Image.file(
                                                      File(controller.storedImagePath.isNotEmpty
                                                          ? controller.storedImagePath.value
                                                          : controller.selectedImagePath.value),
                                                      fit: BoxFit.contain,
                                                      // scale: 1 / controller.scale.value,
                                                      // scale: controller.scale.value,
                                                      width: addMapConatinerWidth,
                                                      height: addMapConatinerHeight,
                                                      // scale: ,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          );
                                        },
                                        onWillAcceptWithDetails: (details) {
                                          // details.offset
                                          return true;
                                        },
                                      ),
                                    ),
                                    ...controller.locations.map((loc) {
                                      // print("Location: ${loc.key}");
                                      // print("loc.dx.value: ${loc.dx.value}");
                                      // print("loc.dy.value: ${loc.dy.value}");
                                      return Positioned(
                                        top: loc.dy.value,
                                        left: loc.dx.value,
                                        child: Draggable(
                                          child: Container(
                                            padding: const EdgeInsets.all(10),
                                            decoration: const BoxDecoration(color: yellow, shape: BoxShape.circle),
                                            child: Text(
                                              loc.key.value,
                                              style: const TextStyle(color: Colors.white, fontSize: 10),
                                            ),
                                          ),
                                          feedback: Icon(Icons.circle, color: yellow.darken(0.25), size: 35),
                                          onDragEnd: (details) {
                                            final renderBox = context.findRenderObject() as RenderBox;
                                            final offsetLocalRelative = renderBox.globalToLocal(details.offset);
                                            if (offsetLocalRelative.dx < 0 ||
                                                offsetLocalRelative.dy < 0 ||
                                                offsetLocalRelative.dx > addMapConatinerWidth ||
                                                offsetLocalRelative.dy > addMapConatinerHeight) return;
                                            controller.updateLocationAfterDraggingEnds(
                                              loc.key.value,
                                              Offset(offsetLocalRelative.dx, offsetLocalRelative.dy),
                                              context,
                                              limitX: addMapConatinerWidth,
                                              limitY: addMapConatinerHeight,
                                            );
                                          },
                                        ),
                                      );
                                    }).toList(),
                                  ],
                                );
                              })
                            : const SizedBox(),
                      ),
                      Column(
                        children: [
                          Obx(() {
                            return Center(
                              child: SizedBox(
                                width: 400,
                                child: Row(
                                  children: [
                                    const Text(
                                      "Rotation",
                                      style: TextStyle(color: Colors.white),
                                    ),
                                    const SizedBox(width: 10),
                                    Expanded(
                                      child: SliderTheme(
                                        data: SliderThemeData(
                                          thumbShape: CustomSliderThumbShape(
                                            thumbRadius: 18.0,
                                            sliderValue: controller.rotation.value.toPrecision(2),
                                            thumbColor: brick,
                                            textColor: Colors.white,
                                          ),
                                        ),
                                        child: Slider(
                                          value: controller.rotation.value,
                                          onChanged: (value) {
                                            controller.rotation.value = value;
                                            controller.locations.refresh();
                                          },
                                          min: -180,
                                          max: 180,
                                          divisions: 360,
                                          label: '${controller.rotation.value.round()}',
                                          activeColor: brick,
                                          inactiveColor: brick.withOpacity(0.7),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }),
                          // const SizedBox(height: 10),
                          // Scale slider
                          Obx(() {
                            return Center(
                              child: SizedBox(
                                width: 400,
                                child: Row(
                                  children: [
                                    const Text(
                                      "Scale",
                                      style: TextStyle(color: Colors.white),
                                    ),
                                    const SizedBox(width: 10),
                                    Expanded(
                                      child: SliderTheme(
                                        data: SliderThemeData(
                                          thumbShape: CustomSliderThumbShape(
                                            thumbRadius: 18.0,
                                            sliderValue: controller.scale.value.toPrecision(2),
                                            thumbColor: brick,
                                            textColor: Colors.white,
                                          ),
                                        ),
                                        child: Slider(
                                          value: controller.scale.value,
                                          onChanged: (value) {
                                            controller.scale.value = value;
                                            controller.locations.refresh();
                                          },
                                          min: 1,
                                          max: 5,
                                          divisions: 49,
                                          label: "Scale",
                                          activeColor: brick,
                                          inactiveColor: brick.withOpacity(0.7),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }),
                        ],
                      )
                    ],
                  ),
          ],
        ),
      );
    });
  }
}

class LocationPosition {
  String id;
  RxString key;
  Rx<Offset> initialPosition;
  RxDouble dx;
  RxDouble dy;

  LocationPosition({
    required this.id,
    required this.key,
    required this.initialPosition,
    required this.dx,
    required this.dy,
  });
}

class AddMapController extends GetxController {
  final isEdit = false.obs;

  final storedImageHeightOffset = !kIsWeb && (Platform.isAndroid || Platform.isIOS) ? 70.0.obs : 115.0.obs;

  final selectedImageHeightOffset = !kIsWeb && (Platform.isAndroid || Platform.isIOS) ? 160.0.obs : 190.0.obs;

  // Store/Retrieve the locations
  final locations = RxList<LocationPosition>();

  // Stored map image bytes
  final storedImageBytes = Rxn<Uint8List>();

  // Stored map image path
  final storedImagePath = "".obs;

  // Stored image height
  final storedImageHeight = RxDouble(0);

  // Selected image height -- first time
  final selectedImageHeight = RxDouble(0);

  // Select image for the first time
  final selectedImagePath = "".obs;

  /// This boolean check if the file that stores the values of locations positions is saved locally or not.
  /// True means "Edit map" state, and false means "Add map" state.
  final jsonFileExists = false.obs;

  /// In the "Edit map" state, we can update the scale, rotation and opacity of the stored image.
  /// This boolean check if image is updated or not.
  final isImageUpdated = false.obs;

  final _parsedScenario = Rxn<Scenario>();

  final rotation = 0.0.obs;
  final scale = 1.0.obs;
  final offset = Offset.zero.obs;

  /// Function to load the locations from a JSON file
  Future<void> loadLocations(Scenario scenario) async {
    // Open directory, list files, get simdef.xml file, parse it
    final dir = Directory(
      (await Directory(scenario.directoryPath).list().toList()).where((element) => !element.path.contains("DS_Store")).toList()[0].path,
    );
    final files = await dir.list().toList();
    final simdefFile = files.firstWhere((file) => file.path.endsWith("simdef.xml"));
    final simdefXml = await File(simdefFile.path).readAsString();
    _parsedScenario.value = await parseSimDef(simdefXml, dir);

    final file = File('${dir.path}/mapinfo.json');

    // If the mapinfo.json file exists, then read locations positions from it, and read simMap.png image
    //else, for each location set LocationPosition(), and initialLocations.
    if (await file.exists()) {
      jsonFileExists.value = true;

      // Read source image
      storedImagePath.value = '${dir.path}/simMap.png';
      storedImageBytes.value = await File('${dir.path}/simMap.png').readAsBytes();

      final image = Image.memory(
        storedImageBytes.value!,
        fit: BoxFit.fill,
      );

      // We extract the image width and height using the completer to get "image ratio" whcihc will be used to update "storedImageHeight" value
      Completer<ui.Image> completer = Completer<ui.Image>();
      image.image.resolve(const ImageConfiguration()).addListener(ImageStreamListener((ImageInfo info, bool _) {
        completer.complete(info.image);
        completer.future.then((value) async {
          final imageRatio = (value.width) / (value.height);
          storedImageHeight.value = Get.size.width * .9 / imageRatio;
          // Read JSON file "mapinfo.json"
          String jsonLocations = await file.readAsString();
          Map<String, dynamic> locationsData = jsonDecode(jsonLocations);
          print(
              "Locations Data: $locationsData has type ${locationsData.runtimeType} and locations has type ${locationsData["locations"].runtimeType} while first entry has type ${locationsData["locations"][0].runtimeType}");
          // Map<String, dynamic> locationsMap = jsonDecode(locationsData["locations"]);
          Map<String, dynamic> locationsMap = (locationsData["locations"] as List<dynamic>).fold(Map<String, dynamic>.new(), (value, element) {
            final key = element["name"] as String;
            value.addAll({
              key: {"dx": element["left"] / 100, "dy": element["top"] / 100, "id": element["id"]}
            });
            return value;
          });

          scale.value = locationsData["scale"];
          rotation.value = locationsData["rotation"];
          offset.value = Offset(locationsData["offset"]["dx"], locationsData["offset"]["dy"]);

          // Assign Offset values for each location and initial location
          locations.addAll(locationsMap.entries.map((e) {
            return LocationPosition(
              id: e.value["id"],
              key: e.key.obs,
              initialPosition: Offset(e.value['dx'] * addMapConatinerWidth, e.value['dy'] * addMapConatinerHeight).obs,
              dx: RxDouble(e.value['dx'] * addMapConatinerWidth),
              dy: RxDouble(e.value['dy'] * addMapConatinerHeight),
              // initialPosition: Offset(e.value['dx'] * storedImageHeight.value, e.value['dy'] * storedImageHeight.value).obs,
              // dx: RxDouble(e.value['dx'] * storedImageHeight.value),
              // dy: RxDouble(e.value['dy'] * storedImageHeight.value),
            );
          }).toList());
        });
      }));
    } else {
      jsonFileExists.value = false;
      initializeLocationsPositions();
    }
  }

  /// Function to update the locations after dragging ends
  void updateLocationAfterDraggingEnds(String key, Offset details, BuildContext context, {required double limitX, required double limitY}) {
    // final newPosition = jsonFileExists.isTrue
    //     ? details.offset - Offset(22, storedImageHeightOffset.value)
    //     : details.offset - Offset(22, selectedImageHeightOffset.value);

    for (var loc in locations) {
      print("Comparing ${loc.key.value} with $key");
      if (loc.key.value == key) {
        print("Matched for details $details");
        // loc.dx.value = ui.clampDouble(newPosition.dx, 0, limitX);
        // loc.dy.value = ui.clampDouble(newPosition.dy, 0, limitY);
        // loc.dx.value = newPosition.dx;
        // loc.dy.value = newPosition.dy;
        loc.dx.value = details.dx;
        loc.dy.value = details.dy;

        // loc.newDxPercentage = (loc.dx.value / (MediaQuery.sizeOf(context).width * .875)).obs;
        // loc.newDyPercentage = jsonFileExists.isTrue ? (loc.dy.value / storedImageHeight.value).obs : (loc.dy.value / selectedImageHeight.value).obs;
      }
    }

    locations.refresh();
  }

  /// Function to save the locations to a JSON file
  Future<void> saveLocations(String path, String selectedImagePath) async {
    isEdit.value = false;
    if (locations.isEmpty) {
      Get.snackbar(
        "Warning",
        "No locations found",
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }
    if (isLocationsChanged() || isImageUpdated.value) {
      // Convert the locations to a JSON string
      Map<String, dynamic> locationsMap = {};
      for (var loc in locations) {
        locationsMap[loc.key.value] = {'dx': loc.dx.value / addMapConatinerWidth, 'dy': loc.dy.value / addMapConatinerHeight, 'id': loc.id};
        /* locationsMap[loc.key.value] = jsonFileExists.value
            ? {'dx': loc.dx.value / storedImageHeight.value, 'dy': loc.dy.value / storedImageHeight.value, 'id': loc.id}
            : {'dx': loc.dx.value / selectedImageHeight.value, 'dy': loc.dy.value / selectedImageHeight.value, 'id': loc.id}; */
      }
      String jsonLocations = jsonEncode({
        "image": "simMap.png",
        "locations": locationsMap.keys
            .map((key) => Map<String, dynamic>.from({
                  "name": key,
                  // "id": locationsMap[key]['id'],
                  "id": key,
                  "left": locationsMap[key]['dx'] * 100,
                  "top": locationsMap[key]['dy'] * 100,
                }))
            .toList(),
        "scale": scale.value,
        "rotation": rotation.value,
        "offset": {
          "dx": offset.value.dx,
          "dy": offset.value.dy,
        },
      });

      // Write the JSON string to a file
      final file = File('$path/mapinfo.json');
      await file.writeAsString(jsonLocations);

      // Save the map image
      selectedImagePath.isEmpty ? null : await File(selectedImagePath).copy("$path/simMap.png");

      resetAllVariables();

      Get.toNamed("/home", preventDuplicates: false);
    } else {
      Get.snackbar(
        "Warning",
        "Please update locations positions",
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }
  }

  /// Function to delete the JSON file
  Future<void> deleteMap(String path) async {
    final jsonFile = File('$path/mapinfo.json');
    final imageFile = File('$path/simMap.png');
    await jsonFile.delete();
    await imageFile.delete();

    resetAllVariables();
    initializeLocationsPositions();

    // Get.toNamed("/home", preventDuplicates: false);
  }

  resetAllVariables() {
    locations.clear();
    selectedImagePath.value = "";
    storedImagePath.value = "";
    storedImageBytes.value = null;
    jsonFileExists.value = false;
    isEdit.value = false;
    scale.value = 1.0;
    rotation.value = 0.0;
    offset.value = Offset.zero;
  }

  initializeLocationsPositions() {
    var uniqueLocationsKeys = <String>{}.obs; // To store unique location identifiers (names)
    var uniqueLocations = <SimulationLocation>[].obs;

    for (int i = 0; i < _parsedScenario.value!.locations.length; i++) {
      final loc = _parsedScenario.value!.locations[i];
      if (uniqueLocationsKeys.contains(loc.name)) continue;
      uniqueLocationsKeys.add(loc.name);
      uniqueLocations.add(loc);
    }

    locations.addAll(uniqueLocations.mapIndexed((index, loc) {
      return LocationPosition(
        id: loc.id,
        key: loc.name.obs,
        initialPosition: Offset(ui.clampDouble(index * 5, 0, 100), 0).obs,
        // dx: 0.0.obs,
        dx: ui.clampDouble(index * 5, 0, 100).obs,
        dy: 0.0.obs,
      );
    }).toList());
  }

  /// Boolean function to check if locations has changed
  bool isLocationsChanged() {
    for (var loc in locations) {
      print("${loc.key} offset: ${Offset(loc.dx.value, loc.dy.value)} -- initialPosition: ${loc.initialPosition.value}");
    }
    for (var loc in locations) {
      if (Offset(loc.dx.value, loc.dy.value) != loc.initialPosition.value) {
        print("Not Equal");
        return true;
      }
    }
    print("Equal");
    return false;
  }

  /// Select image from inside user's device
  selectImage(imagePathList) {
    if (imagePathList.isEmpty) return;
    selectedImagePath.value = imagePathList[0]!;
  }

  /// Get the height of the image uploaded from user's device -- at first time, then use it to update SelectedImageHeight Value
  /// This method is created to mimic how the old system sets image height
  updateSelectedImageHeightValue(BuildContext context) {
    // Convert image path to Image object
    if (selectedImagePath.value.isNotEmpty) {
      final image = Image.file(
        File(selectedImagePath.value),
        fit: BoxFit.fill,
      );

      // Get the "aspectRatio" of the "Image" using "ImageStream resolve", then update selectedImageHeight using the "Completer"
      Completer<ui.Image> completer = Completer<ui.Image>();
      image.image.resolve(const ImageConfiguration()).addListener(ImageStreamListener((ImageInfo info, bool _) {
        completer.complete(info.image);
        completer.future.then((value) {
          final aspectRatio = (value.width) / (value.height);
          selectedImageHeight.value = MediaQuery.sizeOf(context).width * .9 / aspectRatio;
        });
      }));
    }
  }
}

class ColorFilterGenerator {
  static List<double> brightnessAdjustMatrix({required double value}) {
    if (value <= 0) {
      value = value * 255;
    } else {
      value = value * 100;
    }

    if (value == 0) {
      return [1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0];
    }

    return List<double>.from(<double>[1, 0, 0, 0, value, 0, 1, 0, 0, value, 0, 0, 1, 0, value, 0, 0, 0, 1, 0]).map((i) => i.toDouble()).toList();
  }
}
