import 'package:flame/components.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:simsushare_player/components/SimPlayer.dart' if (dart.library.html) 'package:simsushare_player/components/SimPlayerWeb.dart';
import 'package:simsushare_player/utils/constants.dart';

Future<SimSpriteComponent> buildContainer({required String type, required int view, Vector2? size}) async {
  final assetPath = "assets/containers/${containerAssetsMapping[type]}/${view.toString()}.png";
  final imgAsset = await rootBundle.load(assetPath);
  final img = await decodeImageFromList(imgAsset.buffer.asUint8List());
  print("Container $type has width: ${img.width} and height: ${img.height}");
  final widthToHeightRatio = img.height / img.width;
  final sprite = SimSpriteComponent(
    onDragged: (p0, p1) {},
    onTapped: (p0, p1) {},
  )
    ..sprite = Sprite(img)
    ..size = size != null ? Vector2(size.x, size.x * widthToHeightRatio) : Vector2(img.width.toDouble(), img.height.toDouble());
  return sprite;
}
