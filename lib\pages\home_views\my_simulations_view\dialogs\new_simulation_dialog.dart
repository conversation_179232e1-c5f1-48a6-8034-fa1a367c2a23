import 'dart:convert';
import 'dart:io';

import 'package:collection/collection.dart';
import 'package:file_picker/file_picker.dart' show FileType;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';

import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/GreyButton.dart';
import 'package:simsushare_player/components/LibraryDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/utils/constants.dart' as constants;
import 'package:simsushare_player/utils/constants.dart';
import 'package:simsushare_player/utils/picker.dart';
import 'package:simsushare_player/utils/preppers.dart';

class NewSimulationDialog extends StatelessWidget {
  NewSimulationDialog({
    Key? key,
  }) : super(key: key);

  final Rx<File?> _initialImage = Rxn<File>();
  final newStateName = "".obs;
  final newSimCategory = "".obs;
  final locationCount = 1.obs;

  final categories = [].obs;

  _initialize() async {
    final response = await http.get(Uri.parse("${constants.baseurl}/category"));
    if (response.statusCode != 200) {
      return print("Error fetching categories: ${response.statusCode} - ${response.body}");
    }
    categories.value = jsonDecode(response.body)["categories"] as List<dynamic>;
    categories.refresh();
  }

  @override
  Widget build(BuildContext context) {
    final SimController _simController = Get.find();
    // _initialize();
    _simController.currentSim.value = Scenario(
      name: "",
      locations: [],
      // width: Get.width - 260,
      // height: Get.height - 100,
      width: editorWidth.toDouble(),
      height: editorHeight.toDouble(),
    );
    final deleteIconFocuNode = FocusNode();
    return ContentDialog(
      title: "Create Simulation",
      content: Container(
        color: sidebarDark,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 28),
              child: Row(
                children: [
                  //"Simulation Name" textfield
                  Expanded(
                    flex: 9,
                    child: _SimulationNameTextFieldWidget(simController: _simController),
                  ),
                  // const SizedBox(width: 10),
                  //"All Categories" dropdown
                  // _AllCategoriesDropdownWidget(newSimCategory: newSimCategory, categories: categories),
                ],
              ),
            ),
            //"Location" Header -- appears after selection
            _LocationHeaderWidget(simController: _simController),
            const SizedBox(height: 20),
            //Locations list
            Obx(() {
              final Map<int, SimulationLocation> baseLocations = {};
              _simController.currentSim.value!.locations.forEachIndexed((index, location) {
                if (baseLocations[index] == null) {
                  baseLocations[index] = location;
                }
              });
              return Expanded(
                child: SingleChildScrollView(
                  child: SizedBox(
                    height: baseLocations.isEmpty ? 220 : 380,
                    child: Column(
                      children: [
                        //Selected images
                        Expanded(
                          child: ReorderableListView(
                              buildDefaultDragHandles: false,
                              children: baseLocations.values
                                  .mapIndexed(
                                    (index, location) => Container(
                                      key: ValueKey(location.id),
                                      color: lightBackgrounds,
                                      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                                      margin: const EdgeInsets.only(bottom: 8),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          ReorderableDragStartListener(
                                            child: const Icon(Icons.drag_indicator, color: Colors.white, size: 18),
                                            index: index,
                                          ),
                                          //Selected location image
                                          Transform.rotate(
                                            angle: location.imageRotation,
                                            child: kIsWeb
                                                ? Image.memory(
                                                    base64Decode(location.image),
                                                    fit: BoxFit.contain,
                                                    height: 64,
                                                    width: 94,
                                                  )
                                                : Image.file(
                                                    File(location.image),
                                                    fit: BoxFit.contain,
                                                    height: 64,
                                                    width: 94,
                                                  ),
                                          ),
                                          //"Location name" Textfield
                                          Expanded(
                                            child: Container(
                                              padding: const EdgeInsets.symmetric(horizontal: 16),
                                              child: TextField(
                                                decoration: const InputDecoration(
                                                  hintText: "Location name",
                                                  enabledBorder: InputBorder.none,
                                                  focusedBorder: InputBorder.none,
                                                  contentPadding: EdgeInsets.zero,
                                                ),
                                                style: const TextStyle(color: Colors.white),
                                                onChanged: (value) => changeLocationNameMethod(value, index, _simController),
                                              ),
                                            ),
                                          ),
                                          //"Delete Location" button
                                          TextButton(
                                            onPressed: () => deleteLocationMethod(index, _simController),
                                            focusNode: deleteIconFocuNode,
                                            onFocusChange: (value) {
                                              if (value) {
                                                deleteIconFocuNode.nextFocus();
                                              }
                                            },
                                            child: Container(
                                              padding: const EdgeInsets.all(10),
                                              decoration: BoxDecoration(color: mainBackgrounds, borderRadius: BorderRadius.circular(50)),
                                              width: 44,
                                              height: 44,
                                              child: const Icon(Icons.delete, size: 18, color: Colors.white),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  )
                                  .toList(),
                              onReorder: (oldIndex, newIndex) {
                                if (oldIndex < newIndex) {
                                  newIndex -= 1;
                                }
                                final item = _simController.currentSim.value!.locations.removeAt(oldIndex);
                                _simController.currentSim.value!.locations.insert(newIndex, item);
                                _simController.currentSim.refresh();
                              }),
                        ),
                        Column(
                          children: [
                            //"Upload images (Locations)" Text
                            const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  "Upload images (Locations)",
                                  style: TextStyle(color: Colors.white),
                                )
                              ],
                            ),
                            const SizedBox(height: 12),
                            Flex(
                              direction: context.isMobileScreen ? Axis.vertical : Axis.horizontal,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                //"Upload From Disk" button
                                GreyButtonLarge(
                                  child: const Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(Icons.upload, color: Colors.white),
                                      SizedBox(width: 14),
                                      Text(
                                        "Upload From Disk",
                                        style: TextStyle(color: Colors.white),
                                      ),
                                    ],
                                  ),
                                  onPressed: () => uploadFromDiskMethod(_simController),
                                ),
                                const SizedBox(width: 12, height: 12),
                                const Text("OR", style: TextStyle(color: Colors.white)),
                                const SizedBox(width: 12, height: 12),
                                //"Select From Library" button
                                GreyButtonLarge(
                                  child: const Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(Icons.image, color: Colors.white),
                                      SizedBox(width: 14),
                                      Text(
                                        "Select From Library",
                                        style: TextStyle(color: Colors.white),
                                      ),
                                    ],
                                  ),
                                  onPressed: () => selectFromLibraryMethod(_simController, context),
                                ),
                              ],
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                ),
              );
            }),
          ],
        ),
      ),
      actions: [
        //"Cancel" button
        TransparentButton(
          label: "Cancel",
          onPressed: () => Get.back(),
        ),

        actionButtonsGap,

        //"Create" button
        OrangeButton(
          label: "Create",
          onPressed: () async {
            print("Sim controller = ${_simController.currentSim.value}");
            if (_simController.currentSim.value!.name == "") {
              Get.showSnackbar(
                const GetSnackBar(
                  title: "Error",
                  message: "Please enter a name for your simulation",
                  duration: Duration(seconds: 2),
                ),
              );
              return;
            }
            if (_simController.currentSim.value!.locations.isEmpty) {
              print("No location created");
              return;
            }
            _simController.currentSim.value!.categoryId = newSimCategory.value;
            _simController.currentLocation.value = 0;
            _simController.currentSim.value!.locations.forEachIndexed((index, element) {
              _simController.currentSim.value!.locations[index].state = _simController.currentSim.value!.states[0].id;
              _simController.currentSim.value!.locations[index].id = element.name + "-" + _simController.currentSim.value!.states[0].id;
            });
            _simController.currentSim.value!.initialLocationId = _simController.currentSim.value!.locations[0].id;
            _simController.currentSim.value!.initialStateId = _simController.currentSim.value!.states[0].id;

            final locationNames = <String>{};
            for (var loc in _simController.currentSim.value!.locations) {
              if (locationNames.contains(loc.name)) {
                Get.showSnackbar(
                  const GetSnackBar(
                    title: "Error",
                    message: "Please enter a unique name for each location",
                    duration: Duration(seconds: 2),
                  ),
                );
                return;
              }
              locationNames.add(loc.name);
            }
            Get.back();
            if (kIsWeb) {
              _simController.currentSim.value!.directoryPath = "new " + DateTime.now().toIso8601String();
              _simController.downloadedSims[_simController.currentSim.value!.directoryPath] = _simController.currentSim.value!;
            }
            Get.toNamed(
              "/create",
              arguments: Map<String, dynamic>.from({}),
              parameters: {"saveOnStart": "true"},
              preventDuplicates: false,
            );
          },
        ),
      ],
    );
  }

  deleteLocationMethod(int index, SimController simController) {
    simController.currentSim.value!.locations.removeAt(index);
    simController.currentSim.refresh();
  }

  changeLocationNameMethod(String value, int index, SimController simController) {
    print("Updating value $value");
    if (value.isEmpty) {
      simController.currentSim.value!.locations[index].name = "Location ${index + 1}";
    } else {
      simController.currentSim.value!.locations[index].name = value;
    }
    // scenarioName.value = value;
    print("New value ${simController.currentSim.value!.locations[index].name}");
    simController.currentSim.refresh();
  }

  uploadFromDiskMethod(SimController simController) async {
    try {
      // await Future.delayed(Duration(milliseconds: 500), () async {});
      print("inital dir: ${kIsWeb ? null : (await getApplicationDocumentsDirectory()).path}");
      final result = await pickFiles(type: FileType.image, allowMultiple: true);
      print("Pick location image result: $result");
      if (result == null) {
        print("No files selected");
        return;
      }
      final decodedImages = await Future.wait(result.files.map((file) async {
        if (file.bytes == null) {
          if (kIsWeb) {
            throw Exception("File bytes are null");
          }
          if (file.path == null) {
            throw Exception("File path is null");
          }
          return decodeImageFromList(await File(file.path!).readAsBytes());
        }
        final imgData = await decodeImageFromList(file.bytes!);
        return imgData;
      }));
      result.files.forEachIndexed((index, currentFile) {
        SimulationLocation newSimLocation;
        if (kIsWeb) {
          print("Setting to base64");
          newSimLocation = SimulationLocation(
            name: "Location $locationCount",
            sprites: [],
            image: base64Encode(currentFile.bytes!.toList(growable: false)),
          );
        } else {
          File file = File(result.paths[index]!);
          _initialImage.value = file;
          newSimLocation = makeLocationFromImage(
            sim: simController.currentSim.value!,
            img: decodedImages[index],
            imgPath: file.path,
            name: "Location $locationCount",
          );
          /* final widthScale = simController.currentSim.value!.width / decodedImages[index].width;
          final heightScale = simController.currentSim.value!.height / decodedImages[index].height;
          print("====== Location $index has Width scale: $widthScale, Height scale: $heightScale");
          newSimLocation = SimulationLocation(
            name: "Location $locationCount",
            sprites: [],
            image: file.path,
            imageScale: max(widthScale, heightScale),
          ); */
        }
        final simLocationsToAdd = simController.currentSim.value!.states.map((state) {
          final sl = newSimLocation.copy();
          sl.state = state.id;
          return sl;
        });
        simController.currentSim.value!.locations.addAll(simLocationsToAdd);
        locationCount.value++;
      });
      simController.currentSim.refresh();
      // print("Locations: ${simController.currentSim.value!.locations}");
    } catch (err) {
      print(err);
    }
  }

  selectFromLibraryMethod(SimController simController, BuildContext context) async {
    List<FileSystemEntity>? files = await showDialog(
      context: context,
      builder: ((context) {
        return LibraryDialog(
          extensionFilter: const [".jpg", ".jpeg", ".gif", ".png"],
        );
      }),
    );
    if (files == null) return;
    for (final file in files) {
      final decodedImage = await decodeImageFromList(await File(file.path).readAsBytes());
      final newSimLocation = makeLocationFromImage(
        sim: simController.currentSim.value!,
        img: decodedImage,
        imgPath: file.path,
        name: "Location $locationCount",
      );
      /* final widthScale = simController.currentSim.value!.width / decodedImage.width;
      final heightScale = simController.currentSim.value!.height / decodedImage.height;
      final newSimLocation = SimulationLocation(
        name: "Location $locationCount",
        sprites: [],
        image: file.path,
        imageScale: max(widthScale, heightScale),
      ); */
      final simLocationsToAdd = simController.currentSim.value!.states.map((state) {
        final sl = newSimLocation.copy();
        sl.state = state.id;
        return sl;
      });
      simController.currentSim.value!.locations.addAll(simLocationsToAdd);
      locationCount.value++;
    }
    simController.currentSim.refresh();
  }
}

class _LocationHeaderWidget extends StatelessWidget {
  const _LocationHeaderWidget({
    Key? key,
    required SimController simController,
  })  : _simController = simController,
        super(key: key);

  final SimController _simController;

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => _simController.currentSim.value!.locations.isEmpty
          ? const SizedBox()
          : const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 8),
                Text("Location", style: headingTextStyle),
                Text("Please provide the scenario location details", style: TextStyle(color: white60, fontSize: 12)),
              ],
            ),
    );
  }
}

class _AllCategoriesDropdownWidget extends StatelessWidget {
  const _AllCategoriesDropdownWidget({
    Key? key,
    required this.newSimCategory,
    required this.categories,
  }) : super(key: key);

  final RxString newSimCategory;
  final RxList categories;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: white60),
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 10),
      child: Obx(
        () => DropdownButton(
          underline: const SizedBox(),
          dropdownColor: constants.lightBackgrounds,
          value: newSimCategory.value,
          items: [
            const DropdownMenuItem(
              child: Text(
                "All Categories",
                style: TextStyle(color: Colors.white),
              ),
              value: "",
            ),
            ...categories
                .map(
                  (element) => DropdownMenuItem(
                    child: Text(
                      element["name"],
                      style: const TextStyle(color: Colors.white),
                    ),
                    value: element["_id"],
                  ),
                )
                .toList()
          ],
          onChanged: (value) {
            newSimCategory.value = value as String;
          },
          hint: const Text("Simulation Category", style: TextStyle(color: Colors.white60)),
        ),
      ),
    );
  }
}

class _SimulationNameTextFieldWidget extends StatelessWidget {
  const _SimulationNameTextFieldWidget({
    Key? key,
    required SimController simController,
  })  : _simController = simController,
        super(key: key);

  final SimController _simController;

  @override
  Widget build(BuildContext context) {
    return TextField(
      onChanged: (value) {
        _simController.currentSim.value!.name = value;
      },
      decoration: const InputDecoration(
        hintText: "Simulation Name",
        contentPadding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
      ),
      style: textFieldTextStyle,
    );
  }
}
