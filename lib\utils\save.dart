import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:collection/collection.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/utils/parsers.dart';

Future<void> savetoDir(Directory dir, Scenario sim) async {
  final validFileNames = <String>[];
  await Future.wait(sim.locations.mapIndexed((index, location) async {
    // print("Copying ${location.image} to ${dir.path}/BK$index${path.extension(location.image)}");
    if (location.image.isNotEmpty) {
      final backgroundImageName = "BK$index${path.extension(location.image)}";
      // await File(location.image).copy("${dir.path}/$backgroundImageName");
      final fileContents = await File(location.image).readAsBytes();
      await File("${dir.path}/$backgroundImageName").writeAsBytes(fileContents);
      validFileNames.add("BK$index${path.extension(location.image)}");
    }
    await Future.wait(location.images.map((image) async {
      // NOTE: consider breaking if the id matches the name of the file
      final imgFileName = "${dir.path}/${image.id}${path.extension(image.path)}";
      validFileNames.add(path.basename(imgFileName));
      await File(image.path).copy(imgFileName);
      image.path = imgFileName;
    }));
    await Future.wait(location.sounds.map((sound) async {
      validFileNames.add(path.basename(sound.path));
      // TODO: maybe also add an exception to imported sound files tha tmay have an absolute path
    }));
  }));
  final simDef = generateSimDef(sim, cleanup: true);
  print(simDef);
  await File("${dir.path}/simdef.xml").writeAsString(simDef);
  validFileNames.addAll(["simdef.xml", "locations.json", "locations.png", "locations.jpg", "locations.jpeg"]);

  final files = dir.listSync();
  await Future.wait(files.map((file) async {
    if (file is File && !validFileNames.contains(path.basename(file.path))) {
      print("Deleting ${file.path}");
      await file.delete();
    }
  }));
}
