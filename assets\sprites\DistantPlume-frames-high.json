{"frames": {"smoke_plume_3_large_01": {"frame": {"x": 0, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_02": {"frame": {"x": 265, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_03": {"frame": {"x": 530, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_04": {"frame": {"x": 795, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_05": {"frame": {"x": 1060, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_06": {"frame": {"x": 1325, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_07": {"frame": {"x": 1590, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_08": {"frame": {"x": 1855, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_09": {"frame": {"x": 2120, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_10": {"frame": {"x": 2385, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_11": {"frame": {"x": 2650, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_12": {"frame": {"x": 2915, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_13": {"frame": {"x": 3180, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_14": {"frame": {"x": 3445, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_15": {"frame": {"x": 3710, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_16": {"frame": {"x": 3975, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_17": {"frame": {"x": 4240, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_18": {"frame": {"x": 4505, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_19": {"frame": {"x": 4770, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_20": {"frame": {"x": 5035, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_21": {"frame": {"x": 5300, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_22": {"frame": {"x": 5565, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_23": {"frame": {"x": 5830, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_24": {"frame": {"x": 6095, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_25": {"frame": {"x": 6360, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_26": {"frame": {"x": 6625, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_27": {"frame": {"x": 6890, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_28": {"frame": {"x": 7155, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_29": {"frame": {"x": 7420, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}, "smoke_plume_3_large_30": {"frame": {"x": 7685, "y": 0, "w": 265, "h": 431}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 265, "h": 431}, "sourceSize": {"w": 265, "h": 431}}}, "meta": {"app": "https://www.codeandweb.com/texturepacker", "version": "1.0", "image": "Output.png", "format": "RGBA8888", "size": {"w": 7950, "h": 431}, "scale": "1", "smartupdate": "$TexturePacker:SmartUpdate:ece443abcfafe666462307ccc3e7fae8:d4e201260d888f3deedc7f99a411b3cf:ab242ca87e7871abc98cd1fb098a82c9$"}}