import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:file_selector/file_selector.dart';
import 'package:flutter/foundation.dart';

String _fileTypeToFileFilter(FileType type, List<String>? allowedExtensions) {
  switch (type) {
    case FileType.any:
      return '';
    case FileType.audio:
      return '"aac", "midi", "mp3", "ogg", "wav"';
    case FileType.custom:
      return '"", "${allowedExtensions!.join('", "')}"';
    case FileType.image:
      return '"bmp", "gif", "jpeg", "jpg", "png"';
    case FileType.media:
      return '"avi", "flv", "mkv", "mov", "mp4", "mpeg", "webm", "wmv", "bmp", "gif", "jpeg", "jpg", "png"';
    case FileType.video:
      return '"avi", "flv", "mkv", "mov", "mp4", "mpeg", "webm", "wmv"';
    default:
      throw Exception('unknown file type');
  }
}

List<String> _fileFilterToSelector(String fileFilter) {
  return fileFilter.split(', ').map((e) => e.replaceAll("\"", "")).toList();
}

Future<FilePickerResult?> _fetcher(FileType type, List<String>? allowedExtensions) async {
  final e = (await openFile(
    acceptedTypeGroups: [
      XTypeGroup(
        extensions: _fileFilterToSelector(
          _fileTypeToFileFilter(type, allowedExtensions),
        ),
      ),
    ],
  ));
  if (e == null) return null;
  return FilePickerResult([
    PlatformFile(
      name: e.name,
      size: await e.length(),
      bytes: await e.readAsBytes(),
      path: e.path,
    )
  ]);
}

Future<FilePickerResult?> pickFiles({FileType type = FileType.any, List<String>? allowedExtensions, bool allowMultiple = false}) async {
  if (allowedExtensions != null && type == FileType.any) {
    type = FileType.custom;
  }
  if (!kIsWeb && Platform.isMacOS) {
    if (allowMultiple) {
      return FilePickerResult(await Future.wait((await openFiles(
        acceptedTypeGroups: [
          XTypeGroup(
            extensions: _fileFilterToSelector(_fileTypeToFileFilter(type, allowedExtensions)),
          )
        ],
      ))
          .map((e) async => PlatformFile(
                name: e.name,
                size: await e.length(),
                bytes: await e.readAsBytes(),
                path: e.path,
              ))
          .toList()));
    }
    return _fetcher(type, allowedExtensions);
  }
  return FilePicker.platform.pickFiles(
    type: type,
    allowedExtensions: allowedExtensions,
    allowMultiple: allowMultiple,
  );
}

/* Future<FilePickerResult?> _pickFile({FileType type = FileType.any, List<String>? allowedExtensions}) async {
  return Platform.isMacOS
      ? _fetcher()
      : FilePicker.platform.pickFiles(
          type: type,
          allowMultiple: false,
        );
} */
