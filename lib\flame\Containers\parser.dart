import 'package:flame/extensions.dart';
import 'package:flutter/material.dart';
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/utils/constants.dart';

SimContainer parseContainer(String type, String text, Map<String, String> props) {
  final data = text.split(",");
  int index = -1;
  final vList = containerAssetsMapping.values.toList();
  for (var i = 0; i < vList.length; i++) {
    if (vList.toList()[i] == type) {
      index = i;
      break;
    }
  }
  return SimContainer(
    type: containerAssetsMapping.keys.toList()[index],
    id: props["id"] ?? "",
    name: props["name"] ?? props["id"] ?? "",
    view: int.tryParse(data[4]) ?? 0,
    /* 
      NOTE: position is saved as is. Might need to revise old app saving position.
      If it's a percentage, we should pass the sim width and height to extract the
      percentage value. We cannot assume the sim width and height is 1200x800 like 
      the old implementation below.
    */
    x: (double.tryParse(props["x"] ?? "") ?? 150),
    y: (double.tryParse(props["y"] ?? "") ?? 150),
    // NOTE: expects size to be 1200x800
    /* x: (double.tryParse(props["x"] ?? "") ?? 150) / 1200,
    y: (double.tryParse(props["y"] ?? "") ?? 150) / 800, */
  )
    ..filterColor = Color(int.tryParse(data[7]) ?? 0xFF000000)
        .withOpacity(double.tryParse(data[8]) ?? 0.0) /* .brighten((double.tryParse(data[3]) ?? 0.0) / 255) */
    ..widthScale = double.tryParse(props["scaleX"] ?? "") ?? 1.0
    ..heightScale = double.tryParse(props["scaleY"] ?? "") ?? 1.0
    ..fadeInWhen = double.tryParse(data[5]) ?? 0.0
    ..fadeInDuration = double.tryParse(data[6]) ?? 0.0
    ..mirrorY = data[12] == "true"
    ..mirrorX = data[13] == "true"
    ..scale = double.tryParse(data[14]) ?? 1.0
    ..blur = double.tryParse(data[15]) ?? 0.0
    ..fadeOut = data[16] == "true"
    ..fadeOutWhen = double.tryParse(data[17]) ?? 0.0
    ..fadeOutDuration = double.tryParse(data[18]) ?? 0.0
    ..movable = props["movable"] == "true";
}

int getContainerView(String type, Map<String, String> props) {
  final data = type.split(",");
  return int.parse(data[4]);
}

String getContainerText(SimContainer container) {
  // 10: tapscale
  // 11: syncvar
  return "in,0,0,${container.filterColor.computeLuminance() * 2 - 1},${container.view},${container.fadeInWhen},${container.fadeInDuration},${container.filterColor.value},${container.filterColor.opacity},${container.trigger},0,0,${container.mirrorY},${container.mirrorX},${container.scale},${container.blur},${container.fadeOut},${container.fadeOutWhen},${container.fadeOutDuration}";
}

Map<String, String> getContainerProps(SimContainer container) {
  return {
    "id": container.id,
    "name": container.name,
    "x": container.x.toString(),
    "y": container.y.toString(),
    "scaleX": container.width.toString(),
    "scaleY": container.height.toString(),
  };
}
