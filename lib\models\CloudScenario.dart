import 'package:json_annotation/json_annotation.dart';

part 'CloudScenario.g.dart';

@JsonSerializable()
class CloudScenario {
  @Json<PERSON>ey(name: "_id")
  String id;
  String archiveURL;
  List<String> categories = List<String>.empty(growable: true);
  String downloadURL = "";
  String filesURL = "";
  @JsonKey(name: "id")
  String identifier = "";
  @Json<PERSON>ey(name: "is_public")
  bool isPublic = false;
  List<dynamic> locations = List<dynamic>.empty(growable: true);
  List<dynamic> variables = List<dynamic>.empty(growable: true);
  List<dynamic> locationsStates = List<dynamic>.empty(growable: true);
  String store = "";
  String title = "";
  String uniqueId = "";
  String updateURL = "";
  CloudScenario({
    required this.id,
    this.archiveURL = "",
    this.categories = const [],
    this.downloadURL = "",
    this.filesURL = "",
    this.identifier = "",
    this.isPublic = false,
    this.locations = const [],
    this.variables = const [],
    this.locationsStates = const [],
    this.store = "",
    this.title = "",
    this.uniqueId = "",
    this.updateURL = "",
  });

  factory CloudScenario.fromJson(Map<String, dynamic> json) => _$CloudScenarioFromJson(json);
  Map<String, dynamic> toJson() => _$CloudScenarioToJson(this);
}
