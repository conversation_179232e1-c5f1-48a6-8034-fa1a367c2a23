import 'package:flutter/material.dart';

class OptionsButton extends StatelessWidget {
  final dynamic Function() onTap;
  const OptionsButton({Key? key, required this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          width: 5,
          color: Colors.white,
        ),
        borderRadius: BorderRadius.circular(30),
      ),
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        child: Container(
          decoration: BoxDecoration(color: Colors.transparent, borderRadius: BorderRadius.circular(30)),
          padding: const EdgeInsets.all(8.0),
          child: const Icon(
            Icons.menu,
            size: 36,
            color: Colors.white,
          ),
        ),
        onTap: onTap,
      ),
    );
  }
}
