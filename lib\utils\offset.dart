import 'dart:math' as math;
import 'dart:ui';

List<Offset> rotateOffsetAboutPoint(List<Offset> points, Offset center, double angle) {
  return points.map((e) {
    // translate point back to origin:
    final x = e.dx - center.dx;
    final y = e.dy - center.dy;

    // prepare rotation
    final cosAngle = math.cos(angle);
    final sinAngle = math.sin(angle);

    // rotate point
    final newX = x * cosAngle - y * sinAngle;
    final newY = x * sinAngle + y * cosAngle;

    // translate point back and return new coordinate
    return Offset(newX + center.dx, newY + center.dy);
  }).toList();
}
