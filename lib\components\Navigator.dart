import 'package:flutter/material.dart';
import 'package:get/get.dart';
// import 'package:get_storage/get_storage.dart';
import 'package:simsushare_player/components/NavigationEditorDialog.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/utils/constants.dart';

class SimNavigator extends StatelessWidget {
  final String locId;
  final bool editorMode;
  final Scenario? scenario;

  SimNavigator({
    Key? key,
    required this.locId,
    this.editorMode = false,
    this.scenario,
  }) : super(key: key);

  final hidden = false.obs;

  @override
  Widget build(BuildContext context) {
    final _simController = Get.find<SimController>();
    final sim = scenario ?? _simController.currentSim.value!;
    return Stack(
      children: [
        Obx(
          () => _simController.hideNavigator.value
              ? const SizedBox()
              : Container(
                  color: const Color(0XFF131517).withAlpha(127),
                  padding: const EdgeInsets.all(10),
                  child: GridView.count(
                    crossAxisCount: 3,
                    crossAxisSpacing: 5,
                    mainAxisSpacing: 5,
                    children: [
                      GridTile(child: Builder(builder: (_) {
                        final nav = sim.navigations.firstWhereOrNull((element) => element.from == locId && element.direction == "NW");
                        final enabled = nav != null;
                        final locIndex = sim.locations.indexWhere((loc) => loc.id == nav?.to);
                        return _NavigationButton(
                          editorParams: _EditorParams(editorMode: editorMode, locId: locId, direction: "NW"),
                          onPressed: () {
                            if (!enabled) return;
                            _simController.currentLocation.value = locIndex;
                          },
                          icon: Icons.north_west,
                          enabled: enabled,
                          tooltip: locIndex >= 0 ? sim.locations[locIndex].name : "",
                        );
                      })),
                      GridTile(child: Builder(builder: (_) {
                        final nav = sim.navigations.firstWhereOrNull((element) => element.from == locId && element.direction == "N");
                        final enabled = nav != null;
                        final locIndex = sim.locations.indexWhere((loc) => loc.id == nav?.to);
                        return _NavigationButton(
                          editorParams: _EditorParams(
                            editorMode: editorMode,
                            locId: locId,
                            direction: "N",
                          ),
                          onPressed: () {
                            if (!enabled) return;
                            _simController.currentLocation.value = locIndex;
                          },
                          icon: Icons.north,
                          enabled: enabled,
                          tooltip: locIndex >= 0 ? sim.locations[locIndex].name : "",
                        );
                      })),
                      GridTile(child: Builder(builder: (_) {
                        final nav = sim.navigations.firstWhereOrNull((element) => element.from == locId && element.direction == "NE");
                        final enabled = nav != null;
                        final locIndex = sim.locations.indexWhere((loc) => loc.id == nav?.to);
                        return _NavigationButton(
                          editorParams: _EditorParams(editorMode: editorMode, locId: locId, direction: "NE"),
                          onPressed: () {
                            if (!enabled) return;
                            _simController.currentLocation.value = locIndex;
                          },
                          icon: Icons.north_east,
                          enabled: enabled,
                          tooltip: locIndex >= 0 ? sim.locations[locIndex].name : "",
                        );
                      })),
                      GridTile(child: Builder(builder: (_) {
                        final nav = sim.navigations.firstWhereOrNull((element) => element.from == locId && element.direction == "W");
                        final enabled = nav != null;
                        final locIndex = sim.locations.indexWhere((loc) => loc.id == nav?.to);
                        return _NavigationButton(
                          editorParams: _EditorParams(editorMode: editorMode, locId: locId, direction: "W"),
                          onPressed: () {
                            if (!enabled) return;
                            _simController.currentLocation.value = locIndex;
                          },
                          icon: Icons.west,
                          enabled: enabled,
                          tooltip: locIndex >= 0 ? sim.locations[locIndex].name : "",
                        );
                      })),
                      GridTile(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            Builder(builder: (_) {
                              final nav = sim.navigations.firstWhereOrNull((element) => element.from == locId && element.direction == "UP");
                              final enabled = nav != null;
                              final locIndex = sim.locations.indexWhere((loc) => loc.id == nav?.to);
                              return _NavigationButton(
                                editorParams: _EditorParams(editorMode: editorMode, locId: locId, direction: "UP"),
                                onPressed: () {
                                  if (!enabled) return;
                                  _simController.currentLocation.value = locIndex;
                                },
                                icon: Icons.keyboard_double_arrow_up,
                                small: true,
                                enabled: enabled,
                                tooltip: locIndex >= 0 ? sim.locations[locIndex].name : "",
                              );
                            }),
                            Builder(builder: (_) {
                              final nav = sim.navigations.firstWhereOrNull((element) => element.from == locId && element.direction == "DOWN");
                              final enabled = nav != null;
                              final locIndex = sim.locations.indexWhere((loc) => loc.id == nav?.to);
                              return _NavigationButton(
                                editorParams: _EditorParams(editorMode: editorMode, locId: locId, direction: "DOWN"),
                                onPressed: () {
                                  if (!enabled) return;
                                  _simController.currentLocation.value = locIndex;
                                },
                                icon: Icons.keyboard_double_arrow_down,
                                small: true,
                                enabled: enabled,
                                tooltip: locIndex >= 0 ? sim.locations[locIndex].name : "",
                              );
                            }),
                          ],
                        ),
                      ),
                      GridTile(child: Builder(builder: (_) {
                        final nav = sim.navigations.firstWhereOrNull((element) => element.from == locId && element.direction == "E");
                        final enabled = nav != null;
                        final locIndex = sim.locations.indexWhere((loc) => loc.id == nav?.to);
                        return _NavigationButton(
                          editorParams: _EditorParams(editorMode: editorMode, locId: locId, direction: "E"),
                          onPressed: () {
                            if (!enabled) return;
                            _simController.currentLocation.value = locIndex;
                          },
                          icon: Icons.east,
                          enabled: enabled,
                          tooltip: locIndex >= 0 ? sim.locations[locIndex].name : "",
                        );
                      })),
                      GridTile(child: Builder(builder: (_) {
                        final nav = sim.navigations.firstWhereOrNull((element) => element.from == locId && element.direction == "SW");
                        final enabled = nav != null;
                        final locIndex = sim.locations.indexWhere((loc) => loc.id == nav?.to);
                        return _NavigationButton(
                          editorParams: _EditorParams(editorMode: editorMode, locId: locId, direction: "SW"),
                          onPressed: () {
                            if (!enabled) return;
                            _simController.currentLocation.value = locIndex;
                          },
                          icon: Icons.south_west,
                          enabled: enabled,
                          tooltip: locIndex >= 0 ? sim.locations[locIndex].name : "",
                        );
                      })),
                      GridTile(child: Builder(builder: (_) {
                        final nav = sim.navigations.firstWhereOrNull((element) => element.from == locId && element.direction == "S");
                        final enabled = nav != null;
                        final locIndex = sim.locations.indexWhere((loc) => loc.id == nav?.to);
                        return _NavigationButton(
                          editorParams: _EditorParams(editorMode: editorMode, locId: locId, direction: "S"),
                          onPressed: () {
                            if (!enabled) return;
                            _simController.currentLocation.value = locIndex;
                          },
                          icon: Icons.south,
                          enabled: enabled,
                          tooltip: locIndex >= 0 ? sim.locations[locIndex].name : "",
                        );
                      })),
                      GridTile(child: Builder(builder: (_) {
                        final nav = sim.navigations.firstWhereOrNull((element) => element.from == locId && element.direction == "SE");
                        final enabled = nav != null;
                        final locIndex = sim.locations.indexWhere((loc) => loc.id == nav?.to);
                        return _NavigationButton(
                          editorParams: _EditorParams(editorMode: editorMode, locId: locId, direction: "SE"),
                          onPressed: () {
                            if (!enabled) return;
                            _simController.currentLocation.value = locIndex;
                          },
                          icon: Icons.south_east,
                          enabled: enabled,
                          tooltip: locIndex >= 0 ? sim.locations[locIndex].name : "",
                        );
                      })),
                    ],
                  ),
                ),
        ),
      ],
    );
  }
}

class HideNavigatorButton extends StatelessWidget {
  const HideNavigatorButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final _simController = Get.find<SimController>();
    return Positioned(
      left: 0,
      bottom: 0,
      child: IconButton(
        onPressed: () {
          _simController.hideNavigator.value = !_simController.hideNavigator.value;
        },
        icon: Obx(
          () => Container(
            decoration: BoxDecoration(
              color: _simController.hideNavigator.value ? brick : Colors.transparent,
              borderRadius: BorderRadius.circular(5),
            ),
            alignment: Alignment.center,
            child: Icon(
              Icons.hide_image,
              color: _simController.hideNavigator.value ? Colors.white : white60,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }
}

class _NavigationButton extends StatelessWidget {
  final Function() onPressed;
  final IconData icon;
  final bool enabled;
  final bool small;
  final _EditorParams? editorParams;
  final String? tooltip;

  const _NavigationButton({
    Key? key,
    required this.onPressed,
    required this.icon,
    this.enabled = false,
    this.small = false,
    this.editorParams,
    this.tooltip,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final _simController = Get.find<SimController>();
    return Tooltip(
      message: tooltip,
      child: Container(
        decoration: BoxDecoration(
            shape: small ? BoxShape.rectangle : BoxShape.circle,
            color: enabled ? brick : navigationGrey,
            borderRadius: small ? BorderRadius.circular(4) : null),
        height: small ? 18 : 24,
        width: 24,
        alignment: Alignment.center,
        child: IconButton(
          alignment: Alignment.center,
          onPressed: editorParams?.editorMode ?? false
              ? () async {
                  final result = await Get.dialog(NavigationEditorDialog(locId: editorParams!.locId, direction: editorParams!.direction));
                  if (result == true) {
                    _simController.currentSim.refresh();
                  }
                }
              : onPressed,
          icon: Icon(icon, color: Colors.white, size: small ? 9 : 16),
        ),
      ),
    );
  }
}

class _EditorParams {
  final bool editorMode;
  final String locId;
  final String direction;
  _EditorParams({required this.editorMode, required this.locId, required this.direction}) {
    if (editorMode) {
      if (direction.isEmpty) {
        throw Exception("Direction must be set in editor mode");
      }
      if (locId.isEmpty) {
        throw Exception("Location ID must be set in editor mode");
      }
    }
  }
}
