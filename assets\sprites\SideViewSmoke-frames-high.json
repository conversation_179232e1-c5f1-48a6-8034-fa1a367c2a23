{"frames": {"window-smoke-2-01": {"frame": {"x": 0, "y": 0, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-02": {"frame": {"x": 789, "y": 0, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-03": {"frame": {"x": 1578, "y": 0, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-04": {"frame": {"x": 2367, "y": 0, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-05": {"frame": {"x": 3156, "y": 0, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-06": {"frame": {"x": 3945, "y": 0, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-07": {"frame": {"x": 4734, "y": 0, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-08": {"frame": {"x": 5523, "y": 0, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-09": {"frame": {"x": 6312, "y": 0, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-10": {"frame": {"x": 7101, "y": 0, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-11": {"frame": {"x": 7890, "y": 0, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-12": {"frame": {"x": 8679, "y": 0, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-13": {"frame": {"x": 9468, "y": 0, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-14": {"frame": {"x": 10257, "y": 0, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-15": {"frame": {"x": 11046, "y": 0, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-16": {"frame": {"x": 11835, "y": 0, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-17": {"frame": {"x": 12624, "y": 0, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-18": {"frame": {"x": 13413, "y": 0, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-19": {"frame": {"x": 14202, "y": 0, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-20": {"frame": {"x": 14991, "y": 0, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-21": {"frame": {"x": 0, "y": 1044, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-22": {"frame": {"x": 789, "y": 1044, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-23": {"frame": {"x": 1578, "y": 1044, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-24": {"frame": {"x": 2367, "y": 1044, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-25": {"frame": {"x": 3156, "y": 1044, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-26": {"frame": {"x": 3945, "y": 1044, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-27": {"frame": {"x": 4734, "y": 1044, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-28": {"frame": {"x": 5523, "y": 1044, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-29": {"frame": {"x": 6312, "y": 1044, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-30": {"frame": {"x": 7101, "y": 1044, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-31": {"frame": {"x": 7890, "y": 1044, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-32": {"frame": {"x": 8679, "y": 1044, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-33": {"frame": {"x": 9468, "y": 1044, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-34": {"frame": {"x": 10257, "y": 1044, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-35": {"frame": {"x": 11046, "y": 1044, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-36": {"frame": {"x": 11835, "y": 1044, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-37": {"frame": {"x": 12624, "y": 1044, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-38": {"frame": {"x": 13413, "y": 1044, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-39": {"frame": {"x": 14202, "y": 1044, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-40": {"frame": {"x": 14991, "y": 1044, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-41": {"frame": {"x": 0, "y": 2088, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-42": {"frame": {"x": 789, "y": 2088, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-43": {"frame": {"x": 1578, "y": 2088, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-44": {"frame": {"x": 2367, "y": 2088, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-45": {"frame": {"x": 3156, "y": 2088, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-46": {"frame": {"x": 3945, "y": 2088, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-47": {"frame": {"x": 4734, "y": 2088, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-48": {"frame": {"x": 5523, "y": 2088, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-49": {"frame": {"x": 6312, "y": 2088, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-50": {"frame": {"x": 7101, "y": 2088, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-51": {"frame": {"x": 7890, "y": 2088, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-52": {"frame": {"x": 8679, "y": 2088, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-53": {"frame": {"x": 9468, "y": 2088, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-54": {"frame": {"x": 10257, "y": 2088, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-55": {"frame": {"x": 11046, "y": 2088, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-56": {"frame": {"x": 11835, "y": 2088, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-57": {"frame": {"x": 12624, "y": 2088, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-58": {"frame": {"x": 13413, "y": 2088, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-59": {"frame": {"x": 14202, "y": 2088, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}, "window-smoke-2-60": {"frame": {"x": 14991, "y": 2088, "w": 789, "h": 1044}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 789, "h": 1044}, "sourceSize": {"w": 789, "h": 1044}}}, "meta": {"app": "https://www.codeandweb.com/texturepacker", "version": "1.0", "image": "Output.png", "format": "RGBA8888", "size": {"w": 15780, "h": 3132}, "scale": "0.6", "smartupdate": "$TexturePacker:SmartUpdate:b20cee30ff59cece062090ef9889cada:6832879317026aa17dcb0d7fc1ed76c1:ab242ca87e7871abc98cd1fb098a82c9$"}}