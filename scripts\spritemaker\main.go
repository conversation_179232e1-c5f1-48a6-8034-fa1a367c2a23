package main

import (
	"encoding/json"
	"flag"
	"image"
	"image/color"
	"image/png"
	"log"
	"math"
	"os"
	"strconv"
)

/* type SafeInt struct {
	mu sync.Mutex
	v  int
} */

type manifest struct {
	Meta   meta                   `json:"meta"`
	Frames map[string]framesEntry `json:"frames"`
}

// func (man *manifest) Marshal() ([]byte, error) {
// 	buf := &bytes.Buffer{}
//     buf.Write([]byte{'{', '\n'})
//     l := len(order)
//     for i, k := range order {
//         fmt.Fprintf(buf, "\t\"%s\": \"%v\"", k, om[k])
//         if i < l-1 {
//             buf.WriteByte(',')
//         }
//         buf.WriteByte('\n')
//     }
//     buf.Write([]byte{'}', '\n'})
//     return buf.String()
// }

type meta struct {
	App         string `json:"app"`
	Version     string `json:"version"`
	Size        wh     `json:"size"`
	Image       string `json:"image"`
	Format      string `json:"format"`
	Scale       string `json:"scale"`
	Smartupdate string `json:"smartupdate"`
}

type framesEntry struct {
	Frame            size `json:"frame"`
	Rotated          bool `json:"rotated"`
	Trimmed          bool `json:"trimmed"`
	SpriteSourceSize size `json:"spriteSourceSize"`
	SourceSize       wh   `json:"sourceSize"`
}

type wh struct {
	W int `json:"w"`
	H int `json:"h"`
}

type size struct {
	X int `json:"x"`
	Y int `json:"y"`
	W int `json:"w"`
	H int `json:"h"`
}

func main() {

	name := flag.String("name", "", "Name of sprite")

	outline := flag.Bool("outline", false, "Outline draws an outlined red border around every image")

	skipImageWrite := flag.Bool("skipImageWrite", false, "Skips writing a new image to the output directory")

	perRow := flag.Int("perRow", 10, "Number of images per row")

	flag.Parse()

	if len(*name) == 0 {
		panic("Name of sprite cannot be empty")
	}

	// cpus := runtime.NumCPU()

	dir, err := os.ReadDir("input")
	// dir = dir[:45]
	if err != nil {
		panic(err)
	}

	if len(dir) == 0 {
		panic("Input directory is empty")
	}

	/* largestDivisible := 1
	secondDimension := len(dir)
	for i := 20; i > 0; i-- {
		if len(dir)%i == 0 {
			largestDivisible = i
			secondDimension = len(dir) / i
			return
		}
	} */

	// image := image.NewRGBA(image.Rect(0,0, ))
	file, err := os.Open("./input/" + dir[0].Name())
	if err != nil {
		panic(err)
	}
	img, err := png.Decode(file)
	if err != nil {
		log.Println(file.Name())
		panic(err)
	}
	bounds := img.Bounds()
	err = file.Close()
	if err != nil {
		panic(err)
	}

	smallestX := bounds.Max.X
	smallestY := bounds.Max.Y
	largestX := 0
	largestY := 0

	man := manifest{
		Meta: meta{
			App:         "simsushare.com/inhouse",
			Version:     "1.0",
			Image:       "frames-high.png",
			Format:      "RGBA8888",
			Size:        wh{},
			Scale:       "1",
			Smartupdate: "",
		},
		Frames: map[string]framesEntry{},
	}

	for _, entry := range dir {
		fPath := "./input/" + entry.Name()
		eFile, err := os.Open(fPath)
		if err != nil {
			panic(err)
		}
		defer eFile.Close()
		eImg, err := png.Decode(eFile)
		if err != nil {
			panic(err)
		}
		eBounds := eImg.Bounds()
		for x := 0; x < eBounds.Max.X; x++ {
			for y := 0; y < eBounds.Max.Y; y++ {
				r, g, b, a := eImg.At(x, y).RGBA()
				if r == 0 && g == 0 && b == 0 && a == 0 {
					continue
				}

				if x < smallestX {
					smallestX = x
				}

				if x > largestX {
					largestX = x
				}

				if y < smallestY {
					smallestY = y
				}

				if y > largestY {
					largestY = y
				}
			}
		}
		// log.Println("Finished at:", fPath)
	}

	log.Println("Smallest:", smallestX, ",", smallestY, "\t Largest:", largestX, ",", largestY)

	dx := largestX - smallestX
	dy := largestY - smallestY

	// output := image.NewRGBA(image.Rect(0,0, dx * largestDivisible, dy * secondDimension))
	// output := image.NewNRGBA(image.Rect(0, 0, dx*10, dy*int(math.Ceil(float64(len(dir))/10))))
	output := image.NewNRGBA(image.Rect(0, 0, dx*(*perRow), dy*int(math.Ceil(float64(len(dir))/float64(*perRow)))))

	digits := len(strconv.Itoa(len((dir))))

	for index, entry := range dir {
		fPath := "./input/" + entry.Name()
		// go func(fPath string, index int) {
		eFile, err := os.Open(fPath)
		if err != nil {
			panic(err)
		}
		defer eFile.Close()
		eImg, err := png.Decode(eFile)
		if err != nil {
			panic(err)
		}
		// startingX := dx * (index % 10)
		startingX := dx * (index % *perRow)
		// startingY := int(math.Floor(float64(index)/10)) * dy
		startingY := int(math.Floor(float64(index)/(float64(*perRow)))) * dy
		// log.Print("Index:", index, " , calcX:", (index % 10), " , calcY:", int(math.Floor(float64(index)/10)), " , Starting X:", startingX, " , Starting Y:", startingY)
		indexWithPrefix := strconv.Itoa(index)

		for i := 0; i < digits-len(strconv.Itoa(index)); i++ {
			indexWithPrefix = "0" + indexWithPrefix
		}

		man.Frames[*name+"/"+indexWithPrefix] = framesEntry{
			Rotated: false,
			Trimmed: true,
			Frame: size{
				X: startingX,
				Y: startingY,
				W: dx,
				H: dy,
			},
		}
		if !*skipImageWrite {
			for x := 0; x < dx; x++ {
				for y := 0; y < dy; y++ {
					if x == 0 && y == 0 {
						// log.Print("Index:", index, " , calcX:", (index % 10), " , calcY:", int(math.Floor(float64(index)/10)), " , Starting X:", startingX, " , Starting Y:", startingY)
						log.Print("Index:", index, " , calcX:", (index % (*perRow)), " , calcY:", int(math.Floor(float64(index)/float64(*perRow))), " , Starting X:", startingX, " , Starting Y:", startingY)
					}
					// r, g, b, a := eImg.At(x, y).RGBA()
					// if x == 1 {
					// 	log.Println(r, g, b, a, uint8(r), uint8(g), uint8(b), uint8(a))
					// }
					// output.SetNRGBA(startingX+x, startingY+y, color.NRGBA{R: uint8(r), G: uint8(g), B: uint8(b), A: uint8(a)})
					output.Set(startingX+x, startingY+y, eImg.At(x+smallestX, y+smallestY))
					if *outline && (x == 0 || x == dx-1 || y == 0 || y == dy-1) {
						output.Set(startingX+x, startingY+y, color.RGBA{R: 255, A: 255})
					}
				}
			}
		}
		// }(fPath, index)
	}
	man.Meta.Size = wh{W: output.Bounds().Max.X, H: output.Bounds().Max.Y}
	if !*skipImageWrite {
		err = os.Remove("./output/" + *name + "-frames-high.png")
		if err != nil {
			log.Println(err)
		}
		outputFile, err := os.Create("./output/" + *name + "-frames-high.png")
		if err != nil {
			panic(err)
		}
		log.Println(output.Bounds())
		defer outputFile.Close()
		err = png.Encode(outputFile, output)
		if err != nil {
			panic(err)
		}
	}
	outputJson, err := os.Create("./output/" + *name + "-frames-high.json")
	if err != nil {
		panic(err)
	}
	defer outputJson.Close()
	for k := range man.Frames {
		log.Print(k, ", ")
	}
	outputJsonBytes, err := json.MarshalIndent(man, "", "\t")
	if err != nil {
		panic(err)
	}
	_, err = outputJson.Write(outputJsonBytes)
	if err != nil {
		panic(err)
	}
	log.Println("Done")
}
