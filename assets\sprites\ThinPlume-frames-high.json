{"meta": {"app": "https://simsushare.com/image-reorder", "version": "1.0", "image": "frames-high.png", "format": "RGBA8888", "scale": "1", "size": {"w": 7440, "h": 416, "totalWidth": 8228}}, "frames": {"ThinPlume/0": {"frame": {"x": 0, "y": 0, "w": 247, "h": 415}, "rotated": false, "trimmed": true}, "ThinPlume/1": {"frame": {"x": 248, "y": 0, "w": 247, "h": 415}, "rotated": false, "trimmed": true}, "ThinPlume/2": {"frame": {"x": 496, "y": 0, "w": 247, "h": 415}, "rotated": false, "trimmed": true}, "ThinPlume/3": {"frame": {"x": 744, "y": 0, "w": 248, "h": 415}, "rotated": false, "trimmed": true}, "ThinPlume/4": {"frame": {"x": 992, "y": 0, "w": 248, "h": 414}, "rotated": false, "trimmed": true}, "ThinPlume/5": {"frame": {"x": 1240, "y": 0, "w": 248, "h": 414}, "rotated": false, "trimmed": true}, "ThinPlume/6": {"frame": {"x": 1488, "y": 0, "w": 248, "h": 414}, "rotated": false, "trimmed": true}, "ThinPlume/7": {"frame": {"x": 1736, "y": 0, "w": 248, "h": 414}, "rotated": false, "trimmed": true}, "ThinPlume/8": {"frame": {"x": 1984, "y": 0, "w": 248, "h": 414}, "rotated": false, "trimmed": true}, "ThinPlume/9": {"frame": {"x": 2232, "y": 0, "w": 248, "h": 413}, "rotated": false, "trimmed": true}, "ThinPlume/10": {"frame": {"x": 2480, "y": 0, "w": 246, "h": 413}, "rotated": false, "trimmed": true}, "ThinPlume/11": {"frame": {"x": 2728, "y": 0, "w": 246, "h": 413}, "rotated": false, "trimmed": true}, "ThinPlume/12": {"frame": {"x": 2976, "y": 0, "w": 247, "h": 414}, "rotated": false, "trimmed": true}, "ThinPlume/13": {"frame": {"x": 3224, "y": 0, "w": 246, "h": 414}, "rotated": false, "trimmed": true}, "ThinPlume/14": {"frame": {"x": 3472, "y": 0, "w": 244, "h": 414}, "rotated": false, "trimmed": true}, "ThinPlume/15": {"frame": {"x": 3720, "y": 0, "w": 245, "h": 414}, "rotated": false, "trimmed": true}, "ThinPlume/16": {"frame": {"x": 3968, "y": 0, "w": 246, "h": 415}, "rotated": false, "trimmed": true}, "ThinPlume/17": {"frame": {"x": 4216, "y": 0, "w": 246, "h": 415}, "rotated": false, "trimmed": true}, "ThinPlume/18": {"frame": {"x": 4464, "y": 0, "w": 245, "h": 415}, "rotated": false, "trimmed": true}, "ThinPlume/19": {"frame": {"x": 4712, "y": 0, "w": 246, "h": 415}, "rotated": false, "trimmed": true}, "ThinPlume/20": {"frame": {"x": 4960, "y": 0, "w": 246, "h": 415}, "rotated": false, "trimmed": true}, "ThinPlume/21": {"frame": {"x": 5208, "y": 0, "w": 245, "h": 416}, "rotated": false, "trimmed": true}, "ThinPlume/22": {"frame": {"x": 5456, "y": 0, "w": 245, "h": 415}, "rotated": false, "trimmed": true}, "ThinPlume/23": {"frame": {"x": 5704, "y": 0, "w": 245, "h": 415}, "rotated": false, "trimmed": true}, "ThinPlume/24": {"frame": {"x": 5952, "y": 0, "w": 245, "h": 415}, "rotated": false, "trimmed": true}, "ThinPlume/25": {"frame": {"x": 6200, "y": 0, "w": 245, "h": 415}, "rotated": false, "trimmed": true}, "ThinPlume/26": {"frame": {"x": 6448, "y": 0, "w": 245, "h": 415}, "rotated": false, "trimmed": true}, "ThinPlume/27": {"frame": {"x": 6696, "y": 0, "w": 246, "h": 415}, "rotated": false, "trimmed": true}, "ThinPlume/28": {"frame": {"x": 6944, "y": 0, "w": 247, "h": 415}, "rotated": false, "trimmed": true}, "ThinPlume/29": {"frame": {"x": 7192, "y": 0, "w": 247, "h": 415}, "rotated": false, "trimmed": true}}}