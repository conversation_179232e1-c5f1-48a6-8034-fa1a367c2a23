import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/controllers/UserController.dart';

class RegistrationDialog extends StatelessWidget {
  RegistrationDialog({Key? key}) : super(key: key);

  final error = "".obs;
  final args = Get.arguments;

  @override
  Widget build(BuildContext context) {
    final userController = Get.find<UserController>();
    return ContentDialog(
      title: "Register Device",
      disableCloseButton: userController.isRegistrationRequired.value && !userController.registered.value,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text("Registration Required", style: TextStyle(fontSize: 20, color: Colors.white)),
          const SizedBox(height: 20),
          if (error.value.isNotEmpty) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  error.value,
                  style: const TextStyle(color: Colors.red),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
          TextField(
            decoration: const InputDecoration(
              labelText: "Serial Number",
            ),
            style: const TextStyle(color: Colors.white),
            onChanged: (value) {
              userController.registrationToken.value = value;
            },
          ),
        ],
      ),
      actions: [
        OrangeButton(
          onPressed: () async {
            final registered = await userController.checkRegistrationSerial();
            print("Registered? $registered");
            if (registered) {
              Get.back();
              Get.showSnackbar(const GetSnackBar(
                title: "Success",
                message: "Registration Successful!",
                duration: Duration(seconds: 3),
              ));
            } else {
              error.value = "Invalid Serial Number";
            }
          },
          label: "Register",
        )
      ],
    );
  }
}
