// ignore_for_file: must_be_immutable

import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:path/path.dart' as path;

import 'package:audioplayers/audioplayers.dart';
import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_workers/utils/debouncer.dart';
import 'package:path_provider/path_provider.dart';
import 'package:record/record.dart';
import 'package:simsushare_player/components/ConfirmationDialog.dart';
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/Mask.dart';
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/helpers/sim_animated_dropdown.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/helpers/helper_methods.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/helpers/helper_widgets.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/sliders/custom_slider.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/sliders/sidebar_slider_types_enum.dart';
import 'package:simsushare_player/utils/constants.dart';
import 'package:simsushare_player/utils/timers.dart';

const dropdownTextStyle = TextStyle(
  fontSize: 12,
);

enum RecordingStatus { recording, stopped, paused, playing }

class NewScenarioSidebar extends StatelessWidget {
  final SimController simController;
  Rx<Color> pickerColor;
  FocusNode textFocusNode;
  RxDouble blur = RxDouble(0);
  RxDouble scale = RxDouble(1);
  RxDouble widthScale = RxDouble(1);
  RxDouble heightScale = RxDouble(1);
  RxDouble opacity = RxDouble(1);
  RxBool recorderVisible = RxBool(false);
  RxDouble rotation = RxDouble(0);
  RxDouble fadeInWhen = RxDouble(0);
  RxDouble fadeInDuration = RxDouble(0);
  RxDouble fadeOutWhen = RxDouble(0);
  RxDouble fadeOutDuration = RxDouble(0);
  RxDouble speed = RxDouble(1);

  // required for correct values to be set on the textfields
  final tcDebouncer = Debouncer(delay: const Duration(milliseconds: 100));

  NewScenarioSidebar({
    Key? key,
    required this.simController,
    required this.pickerColor,
    required this.textFocusNode,
  }) : super(key: key);

  Timer? colorPickerDebounce;
  Timer? rotationDebounce;

  final rotationTextController = TextEditingController();
  final timoutTextController = TextEditingController();
  final scaleTextController = TextEditingController();
  final widthTextController = TextEditingController();
  final heightTextController = TextEditingController();
  final speedTextController = TextEditingController();
  final opacityTextController = TextEditingController();
  final blurTextController = TextEditingController();
  final fadeInWhenTextController = TextEditingController();
  final fadeOutWhenTextController = TextEditingController();
  final fadeInDurationTextController = TextEditingController();
  final fadeOutDurationTextController = TextEditingController();

  // Recording
  final record = Record();
  final recordPath = "".obs;
  final recordStatus = RecordingStatus.stopped.obs;
  final audio = AudioPlayer();
  final playing = false.obs;

  var isScrolling = false.obs;
  final scrollController = ScrollController();

  _setTextFieldValues() {
    final simController = Get.find<SimController>();
    final obj = simController.getCurrentSelectedObject();
    if (obj == null) {
      return;
    }

    // Setting the observable value
    scale.value = obj.scale;
    widthScale.value = obj.widthScale;
    heightScale.value = obj.heightScale;
    opacity.value = obj.opacity;
    blur.value = obj.blur;
    fadeInWhen.value = obj.fadeInWhen;
    fadeOutWhen.value = obj.fadeOutWhen;
    fadeInDuration.value = obj.fadeInDuration;
    fadeOutDuration.value = obj.fadeOutDuration;

    // setting the textfield value
    scaleTextController.value = TextEditingValue(text: obj.scale.toString());
    widthTextController.value = TextEditingValue(text: obj.widthScale.toString());
    heightTextController.value = TextEditingValue(text: obj.heightScale.toString());
    opacityTextController.value = TextEditingValue(text: obj.opacity.toString());
    blurTextController.value = TextEditingValue(text: obj.blur.toString());
    fadeInWhenTextController.value = TextEditingValue(text: obj.fadeInWhen.toString());
    fadeOutWhenTextController.value = TextEditingValue(text: obj.fadeOutWhen.toString());
    fadeInDurationTextController.value = TextEditingValue(text: obj.fadeInDuration.toString());
    fadeOutDurationTextController.value = TextEditingValue(text: obj.fadeOutDuration.toString());
    if (obj is SimSprite) {
      speedTextController.value = TextEditingValue(text: obj.speed.toString());
    }
  }

  _editObjectId(SimObject obj, List<SimObject> objects) {
    final simController = Get.find<SimController>();
    // final name = obj.id.obs;
    final name = "".obs;
    switch (obj.runtimeType) {
      case SimImage:
        name.value = (obj as SimImage).id;
        break;
      case SimSprite:
        name.value = (obj as SimSprite).name;
        break;
      case SimShape:
        name.value = (obj as SimShape).id;
        break;
      case SimLocationJumper:
        name.value = (obj as SimLocationJumper).name ??
            (obj.to.isNotEmpty ? "Jumper to ${obj.to}" : "Jumper ${objects.whereType<SimLocationJumper>().length + 1}");
        break;
      case SimSound:
        name.value = (obj as SimSound).id;
        break;
      case SimText:
        name.value = (obj as SimText).id;
        break;
      case SimContainer:
        name.value = (obj as SimContainer).name;
        break;
      case SimLabel:
        name.value = (obj as SimLabel).name;
        break;
      case SimPerson:
        name.value = (obj as SimPerson).name;
        break;
      case SimTimer:
        name.value = (obj as SimTimer).id;
        break;
      default:
        return;
    }
    Get.dialog(
      ContentDialog(
        title: "Edit",
        content: Column(
          children: [
            TextField(
              controller: TextEditingController(text: name.value),
              onChanged: (value) {
                name.value = value;
              },
              style: const TextStyle(color: Colors.white),
            )
          ],
        ),
        actions: [
          OrangeButton(
            label: "Save",
            onPressed: () {
              if (objects.firstWhereOrNull((element) => element.id == name.value && element.id != obj.id) == null) {
                obj.id = name.value;
                switch (obj.runtimeType) {
                  case SimImage:
                    (obj as SimImage).id = name.value;
                    break;
                  case SimSprite:
                    (obj as SimSprite).name = name.value;
                    break;
                  case SimShape:
                    (obj as SimShape).id = name.value;
                    break;
                  case SimLocationJumper:
                    // (obj as SimLocationJumper).id = name.value;
                    (obj as SimLocationJumper).name = name.value.isEmpty ? null : name.value;
                    break;
                  case SimSound:
                    (obj as SimSound).id = name.value;
                    break;
                  case SimText:
                    (obj as SimText).id = name.value;
                    break;
                  case SimContainer:
                    (obj as SimContainer).name = name.value;
                    break;
                  case SimLabel:
                    (obj as SimLabel).name = name.value;
                    break;
                  case SimPerson:
                    (obj as SimPerson).name = name.value;
                    break;
                  case SimTimer:
                    (obj as SimTimer).id = name.value;
                    break;
                  default:
                    throw ErrorSummary("Invalid object type");
                }
              } else {
                return Get.snackbar(
                  "Error",
                  "Name already used",
                  duration: const Duration(seconds: 2),
                  colorText: Colors.white,
                  snackPosition: SnackPosition.BOTTOM,
                );
              }
              simController.currentSim.refresh();
              Get.back();
            },
          ),
          TransparentButton(
            label: "Cancel",
            onPressed: () {
              Get.back();
            },
          )
        ],
      ),
    );
  }

  _editMaskId(Mask obj, List<Mask> objects) {
    final simController = Get.find<SimController>();
    final name = obj.name.obs;
    Get.dialog(
      ContentDialog(
        title: "Edit Mask",
        height: null,
        content: Column(
          children: [
            TextField(
              controller: TextEditingController(text: name.value),
              onChanged: (value) {
                name.value = value;
              },
              style: const TextStyle(color: Colors.white),
            )
          ],
        ),
        actions: [
          OrangeButton(
            label: "Save",
            onPressed: () {
              if (objects.firstWhereOrNull((element) => element.name == name.value && element.id != obj.id) == null) {
                obj.name = name.value;
              } else {
                return Get.snackbar(
                  "Error",
                  "Name already used",
                  duration: const Duration(seconds: 2),
                  colorText: Colors.white,
                  snackPosition: SnackPosition.BOTTOM,
                );
              }
              simController.currentSim.refresh();
              Get.back();
            },
          ),
          TransparentButton(
            label: "Cancel",
            onPressed: () {
              Get.back();
            },
          )
        ],
      ),
    );
  }

  _deleteMaskId(Mask mask) {
    return Get.dialog(
      ConfirmationDialog(
        message: "Are you sure you want to delete mask ${mask.name}",
        onConfirmed: () {
          final simController = Get.find<SimController>();
          if (simController.selectedType.value == SimObjectType.mask) {
            simController.selectedSimObjectIndex.value = -1;
            simController.selectedType.value = null;
          }
          simController.currentSim.value!.masks.remove(mask);
          simController.currentSim.refresh();
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final simController = Get.find<SimController>();
    simController.selectedSimObjectIndex.listen((value) {
      tcDebouncer.call(() {
        _setTextFieldValues();
      });
    });
    simController.selectedType.listen((value) {
      tcDebouncer.call(() {
        _setTextFieldValues();
      });
    });
    simController.currentSim.listen((value) {
      final selected = simController.getCurrentSelectedObject();
      if (selected == null) return;
      blur.value = selected.blur;
      scale.value = selected.scale;
      widthScale.value = selected.widthScale;
      heightScale.value = selected.heightScale;
      opacity.value = selected.opacity;
      rotation.value = selected.rotation;
      fadeInWhen.value = selected.fadeInWhen;
      fadeInDuration.value = selected.fadeInDuration;
      fadeOutWhen.value = selected.fadeOutWhen;
      fadeOutDuration.value = selected.fadeOutDuration;
      if (selected is SimSprite) {
        speed.value = selected.speed;
      }
    });
    simController.signalStream.stream.listen((value) {
      if (value == "recorder-visible-toggle") {
        recorderVisible.value = !recorderVisible.value;
      }
    });
    return Container(
      padding: const EdgeInsets.all(18),
      color: mainBackgrounds,
      width: 280,
      child: Column(
        children: [
          Expanded(
            child: ScrollConfiguration(
              behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
              child: NotificationListener<ScrollNotification>(
                onNotification: (notification) {
                  if (notification is ScrollStartNotification) {
                    isScrolling.value = true;
                  }

                  if (notification is ScrollEndNotification) {
                    isScrolling.value = false;
                  }
                  return isScrolling.value;
                },
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: AbsorbPointer(
                    absorbing: isScrolling.value,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Locations & States popup button
                        LocationsPopupButton(),
                        const Divider(color: white60),
                        // Reoderable list of object [name - edit id - select]
                        Obx(() {
                          final scene = simController.currentSim.value!.locations[simController.currentLocation.value];
                          List<SimObject> objects = <SimObject>[
                            ...scene.images,
                            ...scene.sprites,
                            ...scene.shapes,
                            ...scene.jumpers,
                            ...scene.sounds,
                            ...scene.labels,
                            ...scene.containers,
                            ...scene.people,
                            ...scene.texts,
                            ...scene.timers,
                          ];
                          objects.sort((a, b) => -1 * a.priority.compareTo(b.priority));
                          return SingleChildScrollView(
                            child: SizedBox(
                              height: 150,
                              child: Column(
                                children: [
                                  simController.selectedSimObjectIndex.value != -1
                                      ? Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            //Reset action
                                            if (simController.selectedSimObjectIndex.value != -1)
                                              InkWell(
                                                onTap: () {
                                                  simController.getCurrentSelectedObject()?.reset();
                                                  simController.currentSim.refresh();
                                                  simController.selectedSimObjectIndex.refresh();
                                                },
                                                child: const Row(
                                                  children: [
                                                    Icon(
                                                      CupertinoIcons.refresh,
                                                      color: Colors.white,
                                                      size: 14,
                                                    ),
                                                    SizedBox(width: 8),
                                                    Text(
                                                      "Reset",
                                                      style: TextStyle(
                                                        color: Colors.white,
                                                        fontSize: 14,
                                                        fontWeight: FontWeight.w500,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            const SizedBox(height: 10),
                                            //Remove action
                                            if (simController.selectedSimObjectIndex.value != -1)
                                              const InkWell(
                                                onTap: deleteSelectedObject,
                                                child: Row(
                                                  children: [
                                                    Icon(
                                                      CupertinoIcons.delete,
                                                      color: Colors.white,
                                                      size: 14,
                                                    ),
                                                    SizedBox(width: 8),
                                                    Text(
                                                      "Delete",
                                                      style: TextStyle(
                                                        color: Colors.white,
                                                        fontSize: 12,
                                                        fontWeight: FontWeight.w500,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            // Hide / Show All
                                            InkWell(
                                              child: objects.firstWhereOrNull((obj) => obj.hidden) == null
                                                  ? TextButton.icon(
                                                      onPressed: () {
                                                        for (var obj in objects) {
                                                          obj.hidden = true;
                                                        }
                                                        simController.currentSim.refresh();
                                                      },
                                                      icon: const Icon(
                                                        Icons.visibility_off,
                                                        color: Colors.white,
                                                        size: 14,
                                                      ),
                                                      label: const Text(
                                                        "Hide All",
                                                        style: TextStyle(
                                                          color: Colors.white,
                                                          fontSize: 12,
                                                        ),
                                                      ),
                                                    )
                                                  : TextButton.icon(
                                                      onPressed: () {
                                                        for (var obj in objects) {
                                                          obj.hidden = false;
                                                        }
                                                        simController.currentSim.refresh();
                                                      },
                                                      icon: const Icon(
                                                        Icons.visibility,
                                                        color: Colors.white,
                                                        size: 14,
                                                      ),
                                                      label: const Text(
                                                        "Show All",
                                                        style: TextStyle(
                                                          color: Colors.white,
                                                          fontSize: 12,
                                                        ),
                                                      ),
                                                    ),
                                            ),
                                          ],
                                        )
                                      : // Hide / Show All
                                      InkWell(
                                          child: objects.firstWhereOrNull((obj) => obj.hidden) == null
                                              ? TextButton.icon(
                                                  onPressed: () {
                                                    for (var obj in objects) {
                                                      obj.hidden = true;
                                                    }
                                                    simController.currentSim.refresh();
                                                  },
                                                  icon: const Icon(
                                                    Icons.visibility_off,
                                                    color: Colors.white,
                                                    size: 14,
                                                  ),
                                                  label: const Text(
                                                    "Hide All",
                                                    style: TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 12,
                                                    ),
                                                  ),
                                                )
                                              : TextButton.icon(
                                                  onPressed: () {
                                                    for (var obj in objects) {
                                                      obj.hidden = false;
                                                    }
                                                    simController.currentSim.refresh();
                                                  },
                                                  icon: const Icon(
                                                    Icons.visibility,
                                                    color: Colors.white,
                                                    size: 14,
                                                  ),
                                                  label: const Text(
                                                    "Show All",
                                                    style: TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 12,
                                                    ),
                                                  ),
                                                ),
                                        ),
                                  Expanded(
                                    child: ReorderableListView(
                                      buildDefaultDragHandles: false,
                                      onReorder: (oldIndex, newIndex) {
                                        if (oldIndex < newIndex) {
                                          newIndex -= 1;
                                        }
                                        final target = objects.removeAt(oldIndex);
                                        objects.insert(newIndex, target);
                                        for (var i = 0; i < objects.length; i++) {
                                          objects[i].priority = 1000 - i;
                                        }
                                        simController.currentSim.refresh();
                                      },
                                      children: [
                                        ...objects.mapIndexed(
                                          (index, obj) {
                                            IconData entryIcon;
                                            String name;
                                            Function onClick;
                                            switch (obj.runtimeType) {
                                              case SimImage:
                                                entryIcon = Icons.image;
                                                name = obj.id;
                                                onClick = () {
                                                  final index = scene.images.indexWhere((element) => element.id == obj.id);
                                                  simController.selectedSimObjectIndex.value = index;
                                                  simController.selectedType.value = SimObjectType.image;
                                                };
                                                break;
                                              case SimSprite:
                                                entryIcon = Icons.animation;
                                                name = (obj as SimSprite).name;
                                                onClick = () {
                                                  final index = scene.sprites.indexWhere((element) => element.id == obj.id);
                                                  simController.selectedSimObjectIndex.value = index;
                                                  simController.selectedType.value = SimObjectType.sprite;
                                                };
                                                break;
                                              case SimShape:
                                                entryIcon = Icons.category;
                                                name = obj.id;
                                                onClick = () {
                                                  final index = scene.shapes.indexWhere((element) => element.id == obj.id);
                                                  simController.selectedSimObjectIndex.value = index;
                                                  simController.selectedType.value = SimObjectType.shape;
                                                };
                                                break;
                                              case SimLocationJumper:
                                                entryIcon = Icons.navigation;
                                                // name = obj.id;
                                                name = (obj as SimLocationJumper).name ??
                                                    (obj.to.isNotEmpty ? "Jumper to ${obj.to}" : "Jumper ${scene.jumpers.length + 1}");
                                                onClick = () {
                                                  final index = scene.jumpers.indexWhere((element) => element.id == obj.id);
                                                  simController.selectedSimObjectIndex.value = index;
                                                  simController.selectedType.value = SimObjectType.locationJumper;
                                                };
                                                break;
                                              case SimSound:
                                                entryIcon = Icons.multitrack_audio;
                                                name = obj.id;
                                                onClick = () {
                                                  final index = scene.sounds.indexWhere((element) => element.id == obj.id);
                                                  simController.selectedSimObjectIndex.value = index;
                                                  simController.selectedType.value = SimObjectType.audio;
                                                };
                                                break;
                                              case SimText:
                                                entryIcon = Icons.text_fields;
                                                name = (obj as SimText).text;
                                                onClick = () {
                                                  final index = scene.texts.indexWhere((element) => element.id == obj.id);
                                                  simController.selectedSimObjectIndex.value = index;
                                                  simController.selectedType.value = SimObjectType.text;
                                                };
                                                break;
                                              case SimContainer:
                                                entryIcon = Icons.animation;
                                                name = (obj as SimContainer).name;
                                                onClick = () {
                                                  final index = scene.containers.indexWhere((element) => element.id == obj.id);
                                                  simController.selectedSimObjectIndex.value = index;
                                                  simController.selectedType.value = SimObjectType.container;
                                                };
                                                break;
                                              case SimLabel:
                                                entryIcon = Icons.animation;
                                                name = (obj as SimLabel).name;
                                                onClick = () {
                                                  final index = scene.labels.indexWhere((element) => element.id == obj.id);
                                                  simController.selectedSimObjectIndex.value = index;
                                                  simController.selectedType.value = SimObjectType.label;
                                                };
                                                break;
                                              case SimPerson:
                                                entryIcon = Icons.animation;
                                                name = (obj as SimPerson).name;
                                                onClick = () {
                                                  final index = scene.people.indexWhere((element) => element.id == obj.id);
                                                  simController.selectedSimObjectIndex.value = index;
                                                  simController.selectedType.value = SimObjectType.person;
                                                };
                                                break;
                                              case SimTimer:
                                                entryIcon = Icons.timer;
                                                name = (obj as SimTimer).id;
                                                onClick = () {
                                                  final index = scene.timers.indexWhere((element) => element.id == obj.id);
                                                  simController.selectedSimObjectIndex.value = index;
                                                  simController.selectedType.value = SimObjectType.timer;
                                                };
                                                break;
                                              default:
                                                return const SizedBox(child: Text("Invalid object type"));
                                            }
                                            final isSelected = simController.getCurrentSelectedObject()?.id == obj.id;
                                            return Row(
                                              key: ValueKey(obj.id),
                                              children: [
                                                ReorderableDragStartListener(
                                                  child: const Icon(
                                                    Icons.drag_indicator,
                                                    color: white60,
                                                    size: 16,
                                                  ),
                                                  index: index,
                                                ),
                                                const SizedBox(width: 8),
                                                Icon(entryIcon, size: 14, color: isSelected ? yellow : Colors.white),
                                                const SizedBox(width: 12),
                                                Expanded(
                                                  child: Text(
                                                    name,
                                                    style: TextStyle(fontSize: 12, color: isSelected ? yellow : Colors.white),
                                                  ),
                                                ),
                                                const SizedBox(width: 5),
                                                IconButton(
                                                  padding: const EdgeInsets.all(4),
                                                  onPressed: () => _editObjectId(obj, objects),
                                                  icon: const Icon(
                                                    Icons.edit_outlined,
                                                    color: white60,
                                                    size: 12,
                                                  ),
                                                  constraints: const BoxConstraints(maxWidth: 26),
                                                ),
                                                IconButton(
                                                  padding: const EdgeInsets.all(4),
                                                  onPressed: () {
                                                    if (isSelected) {
                                                      simController.selectedSimObjectIndex.value = -1;
                                                      simController.selectedType.value = null;
                                                    } else {
                                                      onClick();
                                                    }
                                                    simController.currentSim.refresh();
                                                  },
                                                  icon: Icon(
                                                    isSelected ? Icons.circle : Icons.circle_outlined,
                                                    color: isSelected ? yellow : white60,
                                                    size: 12,
                                                  ),
                                                  constraints: const BoxConstraints(maxWidth: 26),
                                                ),
                                                obj.hidden
                                                    ? IconButton(
                                                        padding: const EdgeInsets.all(4),
                                                        onPressed: () {
                                                          obj.hidden = false;
                                                          simController.currentSim.refresh();
                                                        },
                                                        icon: const Icon(
                                                          Icons.visibility_off_outlined,
                                                          color: white60,
                                                          size: 12,
                                                        ),
                                                        constraints: const BoxConstraints(maxWidth: 26),
                                                      )
                                                    : IconButton(
                                                        padding: const EdgeInsets.all(4),
                                                        onPressed: () {
                                                          obj.hidden = true;
                                                          simController.currentSim.refresh();
                                                        },
                                                        icon: const Icon(
                                                          Icons.visibility,
                                                          color: white60,
                                                          size: 12,
                                                        ),
                                                        constraints: const BoxConstraints(maxWidth: 26),
                                                      ),
                                              ],
                                            );
                                          },
                                        ).toList(),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }),
                        // Mask [name, edit, delete, select]
                        Obx(() {
                          return Column(children: [
                            simController.currentSim.value!.masks.isEmpty ? const SizedBox() : const Divider(color: white60),
                            ...simController.currentSim.value!.masks
                                .where((m) => m.locationId == simController.currentSim.value!.locations[simController.currentLocation.value].id)
                                .mapIndexed(
                              (index, m) {
                                print(
                                    "maskId ${m.id} and mask location id is ${m.locationId} in location: ${simController.currentSim.value!.locations[simController.currentLocation.value].id}");
                                final isSelected =
                                    simController.selectedType.value == SimObjectType.mask && simController.selectedSimObjectIndex.value == index;
                                return Row(
                                  key: ValueKey(m.id),
                                  children: [
                                    Icon(
                                      Icons.theater_comedy,
                                      size: 14,
                                      color: isSelected ? yellow : Colors.white,
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        m.name,
                                        style: TextStyle(fontSize: 14, color: isSelected ? yellow : Colors.white),
                                      ),
                                    ),
                                    const SizedBox(width: 5),
                                    IconButton(
                                      onPressed: () => _editMaskId(m, simController.currentSim.value!.masks),
                                      icon: const Icon(
                                        Icons.edit,
                                        color: white60,
                                        size: 12,
                                      ),
                                    ),
                                    IconButton(
                                      onPressed: () => _deleteMaskId(m),
                                      icon: const Icon(
                                        Icons.delete,
                                        color: white60,
                                        size: 12,
                                      ),
                                    ),
                                    IconButton(
                                      onPressed: () {
                                        if (simController.selectedSimObjectIndex.value == index &&
                                            simController.selectedType.value == SimObjectType.mask) {
                                          simController.selectedSimObjectIndex.value = -1;
                                          simController.selectedType.value = null;
                                        } else {
                                          simController.selectedSimObjectIndex.value = index;
                                          simController.selectedType.value = SimObjectType.mask;
                                        }
                                        simController.currentSim.refresh();
                                        print("Mask selected: $m");
                                      },
                                      icon: isSelected
                                          ? const Icon(
                                              Icons.circle,
                                              color: yellow,
                                              size: 12,
                                            )
                                          : const Icon(
                                              Icons.circle_outlined,
                                              color: white60,
                                              size: 12,
                                            ),
                                    )
                                  ],
                                );
                              },
                            ).toList()
                          ]);
                        }),
                        // Save /Cancel Mask
                        Obx(() {
                          return Visibility(
                            child: Column(
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                      child: OrangeButton(
                                        onPressed: () {
                                          int locationMaskMaxSuffix = 0;
                                          for (var mask in simController.currentSim.value!.masks) {
                                            if (!mask.name.toLowerCase().startsWith("mask-")) {
                                              continue;
                                            }
                                            final maskNameSuffix = mask.name.toLowerCase().split("mask-")[1];
                                            final suffixValue = int.tryParse(maskNameSuffix);
                                            if (suffixValue == null) {
                                              continue;
                                            }
                                            if (locationMaskMaxSuffix < suffixValue) {
                                              locationMaskMaxSuffix = suffixValue;
                                              break;
                                            }
                                          }
                                          final maskName = locationMaskMaxSuffix != 0 ? "Mask-${locationMaskMaxSuffix + 1}" : "Mask-1";
                                          if (simController.newMask.value != null && simController.newMask.value!.coordinates.length > 2) {
                                            simController.newMask.value!.name = maskName;
                                            simController.currentSim.value!.masks.add(simController.newMask.value!);
                                            for (var sprite
                                                in simController.currentSim.value!.locations[simController.currentLocation.value].sprites) {
                                              sprite.maskIds.add(simController.newMask.value!.id);
                                            }
                                            for (var shape in simController.currentSim.value!.locations[simController.currentLocation.value].shapes) {
                                              shape.maskIds.add(simController.newMask.value!.id);
                                            }
                                            simController.newMask.value = null;
                                            simController.newMask.refresh();
                                            simController.currentSim.refresh();
                                          }
                                        },
                                        label: "Save Mask",
                                      ),
                                    )
                                  ],
                                ),
                                const SizedBox(height: 10),
                                Row(
                                  children: [
                                    Expanded(
                                      child: OrangeButton(
                                        onPressed: () {
                                          simController.newMask.value = null;
                                          simController.newMask.refresh();
                                          simController.currentSim.refresh();
                                        },
                                        label: "Cancel Mask",
                                      ),
                                    )
                                  ],
                                ),
                              ],
                            ),
                            visible: simController.newMask.value != null,
                          );
                        }),
                        // Flip mask
                        Obx(
                          () => Visibility(
                            child: Column(
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                      child: OrangeButton(
                                        onPressed: () {
                                          final locationsMasks = simController.currentSim.value!.masks
                                              .where((m) =>
                                                  m.locationId == simController.currentSim.value!.locations[simController.currentLocation.value].id)
                                              .toList();
                                          if (locationsMasks[simController.selectedSimObjectIndex.value].type == MaskType.showOutside) {
                                            locationsMasks[simController.selectedSimObjectIndex.value].type = MaskType.showWithin;
                                          } else {
                                            locationsMasks[simController.selectedSimObjectIndex.value].type = MaskType.showOutside;
                                          }
                                          simController.currentSim.refresh();
                                        },
                                        label: "Flip Mask",
                                      ),
                                    )
                                  ],
                                ),
                                const SizedBox(height: 10),
                                ...(simController.selectedSimObjectIndex.value != -1 && simController.selectedType.value == SimObjectType.mask
                                    ? getAllMaskableObjects()
                                    : []),
                              ],
                            ),
                            visible: simController.selectedSimObjectIndex.value != -1 && simController.selectedType.value == SimObjectType.mask,
                          ),
                        ),
                        // Properties
                        Obx(
                          () => simController.selectedSimObjectIndex.value != -1 && simController.selectedType.value != SimObjectType.mask
                              ? Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Divider(color: white60),
                                    const SizedBox(height: 5),
                                    Text(
                                      "Properties",
                                      style: TextStyle(
                                        color: Colors.white.withOpacity(0.5),
                                        fontSize: 13,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                )
                              : const SizedBox(),
                        ),
                        // Text Input
                        Obx(() {
                          return simController.selectedSimObjectIndex.value != -1 && simController.selectedType.value == SimObjectType.text
                              ? Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      "Text Input",
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const SizedBox(height: 10),
                                    SizedBox(
                                      height: 40,
                                      child: TextField(
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w500,
                                          fontSize: 16,
                                          color: Colors.white,
                                        ),
                                        decoration: InputDecoration(
                                          fillColor: Colors.white.withOpacity(0.05),
                                          filled: true,
                                          hintText: simController.currentSim.value!.locations[simController.currentLocation.value]
                                                  .texts[simController.selectedSimObjectIndex.value].text.isNotEmpty
                                              ? simController.currentSim.value!.locations[simController.currentLocation.value]
                                                  .texts[simController.selectedSimObjectIndex.value].text
                                              : "Add Text",
                                          hintStyle: const TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: 14,
                                            color: Color(0xffBABABA),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderSide: const BorderSide(color: Colors.transparent),
                                            borderRadius: BorderRadius.circular(8),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderSide: const BorderSide(color: Colors.transparent),
                                            borderRadius: BorderRadius.circular(8),
                                          ),
                                        ),
                                        focusNode: textFocusNode,
                                        onChanged: ((value) {
                                          simController.currentSim.value!.locations[simController.currentLocation.value]
                                              .texts[simController.selectedSimObjectIndex.value].text = value;
                                          simController.currentSim.refresh();
                                        }),
                                      ),
                                    ),
                                  ],
                                )
                              : const SizedBox();
                        }),
                        const SizedBox(height: 10),
                        const SizedBox(height: 20),
                        // View
                        Obx(
                          () {
                            return simController.selectedSimObjectIndex.value != -1 && simController.selectedType.value == SimObjectType.container
                                ? SizedBox(
                                    width: 270,
                                    child: DropdownButton<int>(
                                      items: List<String>.filled(
                                        containerViewsMapping[simController.currentSim.value!.locations[simController.currentLocation.value]
                                                    .containers[simController.selectedSimObjectIndex.value].type]
                                                ?.length ??
                                            0,
                                        "",
                                      )
                                          .mapIndexed((index, element) => DropdownMenuItem(
                                                child: Text(
                                                  containerViewsMapping[simController.currentSim.value!.locations[simController.currentLocation.value]
                                                      .containers[simController.selectedSimObjectIndex.value].type]![index],
                                                  style: const TextStyle(color: Colors.white),
                                                ),
                                                value: index,
                                              ))
                                          .toList(),
                                      onChanged: (value) {
                                        simController.currentSim.value!.locations[simController.currentLocation.value]
                                            .containers[simController.selectedSimObjectIndex.value].view = value!;
                                        simController.currentSim.refresh();
                                      },
                                      value: simController.currentSim.value!.locations[simController.currentLocation.value]
                                          .containers[simController.selectedSimObjectIndex.value].view,
                                      hint: const Text("View"),
                                      dropdownColor: lightBackgrounds,
                                    ),
                                  )
                                : const SizedBox();
                          },
                        ),
                        // Label Options
                        Obx(() {
                          if (simController.selectedSimObjectIndex.value != -1 && simController.selectedType.value == SimObjectType.label) {
                            final labelOption = labelOptions[simController.currentSim.value!.locations[simController.currentLocation.value]
                                .labels[simController.selectedSimObjectIndex.value].type];
                            print("Label Option: $labelOption");
                            return SizedBox(
                                width: 270,
                                child: Column(
                                  children: labelOption!.keys.map((key) {
                                    print("Label Option Key: $key");
                                    print("Actual value: ${labelOption[key]}");
                                    if (labelOption[key]!["type"] == "dropdown") {
                                      return DropdownButton<int>(
                                        items: List<String>.filled(
                                          (labelOption[key]!["options"]! as List<String>).length,
                                          "",
                                        )
                                            .mapIndexed((index, element) => DropdownMenuItem(
                                                  child: Text(
                                                    (labelOption[key]!["options"] as List<String>)[index],
                                                    style: const TextStyle(color: Colors.white),
                                                  ),
                                                  value: index,
                                                ))
                                            .toList(),
                                        onChanged: (value) {
                                          simController.currentSim.value!.locations[simController.currentLocation.value]
                                              .labels[simController.selectedSimObjectIndex.value].variables[key] = value!;
                                          simController.currentSim.refresh();
                                        },
                                        value: simController.currentSim.value!.locations[simController.currentLocation.value]
                                            .labels[simController.selectedSimObjectIndex.value].variables[key],
                                        hint: Text(key, style: const TextStyle(color: Colors.white)),
                                        dropdownColor: lightBackgrounds,
                                      );
                                    } else if (labelOption[key]!["type"] == "string") {
                                      return SizedBox(
                                        width: 270,
                                        child: TextField(
                                          controller: TextEditingController.fromValue(
                                            TextEditingValue(
                                                text: simController.currentSim.value!.locations[simController.currentLocation.value]
                                                        .labels[simController.selectedSimObjectIndex.value].variables[key] ??
                                                    ""),
                                          )..selection = TextSelection.fromPosition(
                                              TextPosition(
                                                offset: simController.currentSim.value!.locations[simController.currentLocation.value]
                                                        .labels[simController.selectedSimObjectIndex.value].variables[key]?.length ??
                                                    0,
                                              ),
                                            ),
                                          onChanged: (value) {
                                            String updated = value;
                                            simController.currentSim.value!.locations[simController.currentLocation.value]
                                                .labels[simController.selectedSimObjectIndex.value].variables[key] = updated;
                                            simController.currentSim.refresh();
                                          },
                                          style: const TextStyle(color: Colors.white),
                                          maxLength: labelOption[key]!["maxLength"] as int,
                                        ),
                                      );
                                    } else {
                                      return const SizedBox();
                                    }
                                  }).toList(),
                                ));
                          }
                          return const SizedBox();
                        }),
                        // General Section
                        Obx(
                          () => simController.selectedSimObjectIndex.value != -1 && simController.selectedType.value != SimObjectType.mask
                              ? SimAnimatedDropdown(
                                  title: "General",
                                  defaultOpen: true.obs,
                                  body: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      // Audio control
                                      if (simController.selectedType.value == SimObjectType.audio)
                                        Row(
                                          children: [
                                            const Text(
                                              "Audio",
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 14,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                            const SizedBox(width: 10),
                                            IconButton(
                                              onPressed: () async {
                                                await audio.release();
                                                // audio.setSourceDeviceFile((simController.getCurrentSelectedObject()! as SimSound).path);
                                                await audio.setSource(DeviceFileSource((simController.getCurrentSelectedObject()! as SimSound).path));
                                                await audio.resume();
                                              },
                                              icon: const Icon(
                                                Icons.play_arrow,
                                                color: Colors.white,
                                                size: 20,
                                              ),
                                            ),
                                            const SizedBox(width: 10),
                                            IconButton(
                                              onPressed: () {
                                                audio.stop();
                                              },
                                              icon: const Icon(
                                                Icons.stop,
                                                color: Colors.white,
                                                size: 20,
                                              ),
                                            ),
                                          ],
                                        ),
                                      if (simController.selectedType.value == SimObjectType.audio) const SizedBox(height: 16),
                                      // Rotation
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          const Text(
                                            "Rotation",
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                          const SizedBox(width: 50),
                                          Row(
                                            children: [
                                              InkWell(
                                                onTap: () {
                                                  if (rotation.value >= 270) {
                                                    rotation.value = 0;
                                                    rotationTextController.text = rotation.value.toStringAsFixed(0);
                                                  } else {
                                                    rotation.value += 90;
                                                    rotationTextController.text = rotation.value.toStringAsFixed(0);
                                                  }

                                                  simController.getCurrentSelectedObject()!.rotation = rotation.value;
                                                  simController.currentSim.refresh();
                                                },
                                                child: Image.asset(
                                                  "assets/images/rotate_cw.png",
                                                  color: Colors.white,
                                                  width: 20,
                                                  height: 20,
                                                ),
                                              ),
                                              const SizedBox(width: 8),
                                              InkWell(
                                                onTap: () {
                                                  if (rotation.value <= -270) {
                                                    rotation.value = 0;
                                                    rotationTextController.text = rotation.value.toStringAsFixed(0);
                                                  } else {
                                                    rotation.value -= 90;
                                                    rotationTextController.text = rotation.value.toStringAsFixed(0);
                                                  }
                                                  simController.getCurrentSelectedObject()!.rotation = rotation.value;
                                                  simController.currentSim.refresh();
                                                },
                                                child: Image.asset(
                                                  "assets/images/rotate_ccw.png",
                                                  color: Colors.white,
                                                  width: 20,
                                                  height: 20,
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(width: 10),
                                          // Degree text field
                                          SizedBox(
                                            width: 52,
                                            height: 32,
                                            child: TextField(
                                              controller: rotationTextController,
                                              decoration: InputDecoration(
                                                filled: true,
                                                fillColor: Colors.white.withOpacity(0.05),
                                                contentPadding: const EdgeInsets.only(bottom: 5),
                                                enabledBorder: OutlineInputBorder(
                                                    borderRadius: BorderRadius.circular(8), borderSide: const BorderSide(color: Colors.transparent)),
                                                focusedBorder: OutlineInputBorder(
                                                  borderRadius: BorderRadius.circular(8),
                                                  borderSide: const BorderSide(color: Colors.transparent),
                                                ),
                                              ),
                                              textAlign: TextAlign.center,
                                              textAlignVertical: TextAlignVertical.center,
                                              style: const TextStyle(color: Colors.white, fontSize: 14),
                                              onChanged: (value) {
                                                try {
                                                  double val = double.parse(value);
                                                  if (val < 0) {
                                                    value = "0";
                                                  }
                                                  if (val > 360) {
                                                    value = "360";
                                                  }

                                                  rotation.value = clampDouble(val, 0, 360);
                                                  simController.getCurrentSelectedObject()!.rotation = rotation.value;
                                                  simController.currentSim.refresh();
                                                } catch (e) {
                                                  rotationTextController.clear();
                                                  Get.snackbar(
                                                    "Ooops",
                                                    "You entered a letter! Please enter a number between 0 and 360",
                                                    colorText: Colors.white,
                                                    snackPosition: SnackPosition.BOTTOM,
                                                  );
                                                  return;
                                                }
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 16),
                                      // Mirror / Flip
                                      Obx(
                                        () => simController.selectedSimObjectIndex.value != -1 &&
                                                (simController.selectedType.value == SimObjectType.sprite ||
                                                    simController.selectedType.value == SimObjectType.image ||
                                                    simController.selectedType.value == SimObjectType.locationJumper ||
                                                    simController.selectedType.value == SimObjectType.shape ||
                                                    // simController.selectedType.value == SimObjectType.label ||
                                                    simController.selectedType.value == SimObjectType.person ||
                                                    simController.selectedType.value == SimObjectType.container)
                                            ? SizedBox(
                                                // width: 95,
                                                child: Row(
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: [
                                                    const Text("Mirror / Flip", style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                                                    Row(
                                                      children: [
                                                        GestureDetector(
                                                          onTap: () {
                                                            simController.getCurrentSelectedObject()!.mirrorX =
                                                                !(simController.getCurrentSelectedObject()!.mirrorX);
                                                            simController.currentSim.refresh();
                                                          },
                                                          child: const Tooltip(
                                                            message: "Flip Vertically",
                                                            child: Icon(
                                                              Icons.hourglass_empty_rounded,
                                                              color: Colors.white,
                                                            ),
                                                          ),
                                                        ),
                                                        const SizedBox(width: 8),
                                                        GestureDetector(
                                                          onTap: () {
                                                            simController.getCurrentSelectedObject()!.mirrorY =
                                                                !(simController.getCurrentSelectedObject()!.mirrorY);
                                                            simController.currentSim.refresh();
                                                          },
                                                          child: Transform.rotate(
                                                            angle: pi / 2,
                                                            child: const Tooltip(
                                                              message: "Flip Horizontally",
                                                              child: Icon(Icons.hourglass_empty_rounded, color: Colors.white),
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    )
                                                  ],
                                                ),
                                              )
                                            : const SizedBox(),
                                      ),
                                      const SizedBox(height: 16),
                                      //Scale
                                      Obx(
                                        () =>
                                            simController.selectedSimObjectIndex.value != -1 && simController.selectedType.value != SimObjectType.mask
                                                ? SidebarPanelSlider(
                                                    title: "Scale",
                                                    controller: scaleTextController,
                                                    // sliderType: scale,
                                                    // simController: simController,
                                                    sliderValue: scale,
                                                    sidebarSlider: SidebarSliderProperty.scale,
                                                    sliderColorType: SliderColorType.noGradient,
                                                    minValue: 0.01,
                                                    maxValue: 5,
                                                    onChanged: (newScale) {
                                                      // if (newScale > 5) {
                                                      //   newScale = 5;
                                                      // }

                                                      if (newScale < 0) {
                                                        newScale = 0.01;
                                                      }
                                                      scale.value = newScale;
                                                      scaleTextController.text = newScale.toString();
                                                      simController.getCurrentSelectedObject()!.scale = newScale;
                                                      simController.currentSim.refresh();
                                                    },
                                                  )
                                                : const SizedBox(),
                                      ),
                                      //Widen
                                      Obx(
                                        () => Visibility(
                                          child: SidebarPanelSlider(
                                            title: "Widen",
                                            controller: widthTextController,
                                            sliderValue: widthScale,
                                            // sliderType: widthScale,
                                            // simController: simController,
                                            sidebarSlider: SidebarSliderProperty.widen,
                                            sliderColorType: SliderColorType.noGradient,
                                            minValue: 0.01,
                                            maxValue: 6,
                                            onChanged: (newWidth) {
                                              // if (newWidth > 6) {
                                              //   newWidth = 6;
                                              // }

                                              if (newWidth < 0) {
                                                newWidth = 0;
                                              }
                                              widthScale.value = newWidth;
                                              widthTextController.text = newWidth.toString();
                                              simController.getCurrentSelectedObject()!.widthScale = newWidth;
                                              simController.currentSim.refresh();
                                            },
                                          ),
                                          visible: simController.selectedSimObjectIndex.value != -1 &&
                                              simController.selectedType.value == SimObjectType.sprite,
                                        ),
                                      ),
                                      //Stretch
                                      Obx(
                                        () => Visibility(
                                          child: SidebarPanelSlider(
                                            title: "Stretch",
                                            controller: heightTextController,
                                            // sliderType: heightScale,
                                            // simController: simController,
                                            sliderValue: heightScale,
                                            sidebarSlider: SidebarSliderProperty.stretch,
                                            sliderColorType: SliderColorType.noGradient,
                                            minValue: 0.01,
                                            maxValue: 5,
                                            onChanged: (newStretch) {
                                              // if (newStretch > 6) {
                                              //   newStretch = 6;
                                              // }

                                              if (newStretch < 0) {
                                                newStretch = 0;
                                              }
                                              heightScale.value = newStretch;
                                              heightTextController.text = newStretch.toString();
                                              simController.getCurrentSelectedObject()!.heightScale = newStretch;
                                              simController.currentSim.refresh();
                                            },
                                          ),
                                          visible: simController.selectedSimObjectIndex.value != -1 &&
                                              simController.selectedType.value == SimObjectType.sprite,
                                        ),
                                      ),
                                      //Speed
                                      Obx(
                                        () => Visibility(
                                          child: SidebarPanelSlider(
                                            title: "Speed",
                                            controller: speedTextController,
                                            // sliderType: speed,
                                            // simController: simController,
                                            sliderValue: speed,
                                            sidebarSlider: SidebarSliderProperty.speed,
                                            sliderColorType: SliderColorType.noGradient,
                                            minValue: 0.1,
                                            maxValue: 5,
                                            onChanged: (newSpeed) {
                                              // if (newSpeed > 5) {
                                              //   newSpeed = 5;
                                              // }

                                              if (newSpeed < 0) {
                                                newSpeed = 0;
                                              }
                                              speed.value = newSpeed;
                                              speedTextController.text = newSpeed.toString();
                                              simController.currentSim.value!.locations[simController.currentLocation.value]
                                                  .sprites[simController.selectedSimObjectIndex.value].speed = newSpeed;
                                              simController.currentSim.refresh();
                                            },
                                          ),
                                          visible: simController.selectedSimObjectIndex.value != -1 &&
                                              simController.selectedType.value == SimObjectType.sprite,
                                        ),
                                      ),
                                      // Posture & Sync Variable
                                      Obx(
                                        () {
                                          return simController.selectedSimObjectIndex.value != -1 &&
                                                  (simController.selectedType.value == SimObjectType.person ||
                                                      simController.selectedType.value == SimObjectType.image)
                                              ? Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    // Posture
                                                    if (simController.selectedType.value == SimObjectType.person) ...[
                                                      const Text(
                                                        "Posture",
                                                        style: TextStyle(
                                                          color: Colors.white,
                                                          fontSize: 14,
                                                          fontWeight: FontWeight.w500,
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        width: 270,
                                                        child: DropdownButton<int>(
                                                          isExpanded: true,
                                                          items: peoplePostures[peopleToAssetMapping[simController
                                                                  .currentSim
                                                                  .value!
                                                                  .locations[simController.currentLocation.value]
                                                                  .people[simController.selectedSimObjectIndex.value]
                                                                  .type]]!
                                                              .mapIndexed((index, element) => DropdownMenuItem(
                                                                    child: Text(
                                                                      peoplePostures[peopleToAssetMapping[simController
                                                                          .currentSim
                                                                          .value!
                                                                          .locations[simController.currentLocation.value]
                                                                          .people[simController.selectedSimObjectIndex.value]
                                                                          .type]]![index],
                                                                      style: const TextStyle(color: Colors.white),
                                                                    ),
                                                                    value: index,
                                                                  ))
                                                              .toList(),
                                                          onChanged: (value) {
                                                            simController.currentSim.value!.locations[simController.currentLocation.value]
                                                                .people[simController.selectedSimObjectIndex.value].posture = value!;
                                                            simController.currentSim.refresh();
                                                          },
                                                          value: simController.currentSim.value!.locations[simController.currentLocation.value]
                                                              .people[simController.selectedSimObjectIndex.value].posture,
                                                          hint: const Text("Posture"),
                                                          dropdownColor: lightBackgrounds,
                                                        ),
                                                      ),
                                                      const SizedBox(height: 10),
                                                    ],
                                                    // Link Victim
                                                    const Text(
                                                      "Link Victim/Image",
                                                      style: TextStyle(
                                                        color: Colors.white,
                                                        fontSize: 14,
                                                        fontWeight: FontWeight.w500,
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      width: 270,
                                                      child: DropdownButton<String?>(
                                                        isExpanded: true,
                                                        items: [
                                                          const DropdownMenuItem(
                                                            child: Text(
                                                              "None",
                                                              style: TextStyle(color: Colors.white),
                                                            ),
                                                            value: null,
                                                          ),
                                                          ...simController.currentSim.value!.locations
                                                              .fold(<SimPerson>[], (List<SimPerson> previousValue, loc) {
                                                                for (var person in loc.people) {
                                                                  previousValue.add(person);
                                                                }
                                                                return previousValue;
                                                              })
                                                              .where((element) => element.id != simController.getCurrentSelectedObject()!.id)
                                                              .mapIndexed(
                                                                (index, person) {
                                                                  return DropdownMenuItem(
                                                                    child: Text(
                                                                      "${person.name} (${simController.currentSim.value!.locations.firstWhere((loc) => loc.people.contains(person)).name})",
                                                                      style: const TextStyle(color: Colors.white),
                                                                    ),
                                                                    value: person.id,
                                                                  );
                                                                },
                                                              )
                                                              .toList(),
                                                          ...simController.currentSim.value!.locations
                                                              .fold(<SimImage>[], (List<SimImage> previousValue, loc) {
                                                                for (var img in loc.images) {
                                                                  previousValue.add(img);
                                                                }
                                                                return previousValue;
                                                              })
                                                              .where((element) => element.id != simController.getCurrentSelectedObject()!.id)
                                                              .mapIndexed((index, img) {
                                                                final containingLocation = simController.currentSim.value!.locations
                                                                    .firstWhere((loc) => loc.images.contains(img));
                                                                return DropdownMenuItem(
                                                                  child: Text(
                                                                    "${img.id} (${containingLocation.name})",
                                                                    style: const TextStyle(color: Colors.white),
                                                                  ),
                                                                  value: img.id + "___" + containingLocation.id,
                                                                );
                                                              })
                                                              .toList(),
                                                        ].toList(),
                                                        onChanged: (value) {
                                                          print(
                                                              "Selected sync var value is: $value & the type is ${simController.selectedType.value}");
                                                          if (simController.selectedType.value == SimObjectType.person) {
                                                            simController.currentSim.value!.locations[simController.currentLocation.value]
                                                                .people[simController.selectedSimObjectIndex.value].syncVariable = value;
                                                          } else if (simController.selectedType.value == SimObjectType.image) {
                                                            simController.currentSim.value!.locations[simController.currentLocation.value]
                                                                .images[simController.selectedSimObjectIndex.value].syncVariable = value;
                                                          } else {
                                                            throw Exception("Invalid type for sync variable!");
                                                          }
                                                          simController.currentSim.refresh();
                                                        },
                                                        value: simController.selectedType.value == SimObjectType.person
                                                            ? (simController.currentSim.value!.locations[simController.currentLocation.value]
                                                                .people[simController.selectedSimObjectIndex.value].syncVariable)
                                                            : (simController.currentSim.value!.locations[simController.currentLocation.value]
                                                                .images[simController.selectedSimObjectIndex.value].syncVariable),
                                                        hint: const Text("Sync Variable"),
                                                        dropdownColor: lightBackgrounds,
                                                      ),
                                                    ),
                                                    const SizedBox(height: 10),
                                                    /* // Link Image
                                                    Text(
                                                      "Link Image",
                                                      style: TextStyle(
                                                        color: Colors.white,
                                                        fontSize:  14,
                                                        fontWeight: FontWeight.w500,
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      width: breakpoint == Breakpoints.small ? 180 : 270,
                                                      child: DropdownButton<String?>(
                                                        isExpanded: true,
                                                        items: [
                                                          const DropdownMenuItem(
                                                            child: Text(
                                                              "None",
                                                              style: TextStyle(color: Colors.white),
                                                            ),
                                                            value: null,
                                                          ),
                                                          ...simController.currentSim.value!.locations
                                                              .fold(<SimImage>[], (List<SimImage> previousValue, loc) {
                                                                for (var img in loc.images) {
                                                                  previousValue.add(img);
                                                                }
                                                                return previousValue;
                                                              })
                                                              .where((element) => element.id != simController.getCurrentSelectedObject()!.id)
                                                              .mapIndexed(
                                                                (index, img) => DropdownMenuItem(
                                                                  child: Text(
                                                                    "${img.id} (${simController.currentSim.value!.locations.firstWhere((loc) => loc.images.contains(img)).name})",
                                                                    style: const TextStyle(color: Colors.white),
                                                                  ),
                                                                  value: img.id,
                                                                ),
                                                              )
                                                              .toList(),
                                                        ].toList(),
                                                        onChanged: (value) {
                                                          if(simController.selectedType.value == SimObjectType.person)
                                                          {
                                                            simController.currentSim.value!.locations[simController.currentLocation.value]
                                                                .people[simController.selectedSimObjectIndex.value].syncVariable = value;
                                                          }
                                                          else if(simController.selectedType.value == SimObjectType.image)
                                                          {
                                                            simController.currentSim.value!.locations[simController.currentLocation.value]
                                                                .images[simController.selectedSimObjectIndex.value].syncVariable = value;
                                                          }
                                                          simController.currentSim.value!.locations[simController.currentLocation.value]
                                                              .people[simController.selectedSimObjectIndex.value].syncVariable = value;
                                                          simController.currentSim.refresh();
                                                        },
                                                        value: simController.currentSim.value!.locations[simController.currentLocation.value]
                                                            .people[simController.selectedSimObjectIndex.value].syncVariable,
                                                        hint: const Text("Sync Variable"),
                                                        dropdownColor: lightBackgrounds,
                                                      ),
                                                    ),
                                                    const SizedBox(height: 10), */
                                                  ],
                                                )
                                              : const SizedBox();
                                        },
                                      ),
                                    ],
                                  ),
                                )
                              : const SizedBox(),
                        ),
                        Obx(
                          () => simController.selectedSimObjectIndex.value != -1 && simController.selectedType.value != SimObjectType.mask
                              ? SimAnimatedDropdown(
                                  title: "Color",
                                  body: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      //Sprite Color
                                      Obx(
                                        () => simController.getCurrentSelectedObject() != null &&
                                                simController.selectedSimObjectIndex.value != -1 &&
                                                simController.selectedType.value != SimObjectType.mask
                                            ? Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                    children: [
                                                      const Text(
                                                        "Color",
                                                        style: TextStyle(
                                                          color: Colors.white,
                                                          fontSize: 14,
                                                          fontWeight: FontWeight.w500,
                                                        ),
                                                      ),
                                                      Container(
                                                        width: 70,
                                                        height: 32,
                                                        decoration: BoxDecoration(
                                                          borderRadius: BorderRadius.circular(8),
                                                          color: Colors.white.withOpacity(0.05),
                                                        ),
                                                        child: Center(
                                                          child: Obx(() {
                                                            Color output = simController.getCurrentSelectedObject()!.filterColor;
                                                            return Text(
                                                              colorToHex(
                                                                output,
                                                                includeHashSign: true,
                                                                enableAlpha: false,
                                                                toUpperCase: true,
                                                              ),
                                                              style: const TextStyle(color: Colors.white, fontSize: 14),
                                                            );
                                                          }),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  const SizedBox(height: 10),
                                                  SizedBox(
                                                    height: 410,
                                                    child: IgnorePointer(
                                                      ignoring: isScrolling.value,
                                                      child: ColorPicker(
                                                        pickerColor: pickerColor.value,
                                                        hexInputBar: false,
                                                        showLabel: false,
                                                        displayThumbColor: true,
                                                        enableAlpha: simController.selectedType.value == SimObjectType.shape ? false : true,
                                                        portraitOnly: true,
                                                        pickerAreaHeightPercent: 1.0,
                                                        pickerAreaBorderRadius: const BorderRadius.all(Radius.circular(8)),
                                                        onHsvColorChanged: (value) {
                                                          if (colorPickerDebounce?.isActive ?? false) {
                                                            colorPickerDebounce?.cancel();
                                                          }
                                                          colorPickerDebounce = Timer(const Duration(milliseconds: 200), () {
                                                            if (isScrolling.value) {
                                                              return;
                                                            }
                                                            pickerColor.value = value.toColor();
                                                            simController.getCurrentSelectedObject()!.filterColor = pickerColor.value;
                                                            simController.currentSim.refresh();
                                                          });
                                                        },
                                                        onColorChanged: (Color c) {
                                                          if (colorPickerDebounce?.isActive ?? false) {
                                                            colorPickerDebounce?.cancel();
                                                          }
                                                          colorPickerDebounce = Timer(const Duration(milliseconds: 100), () {
                                                            pickerColor.value = c;
                                                            simController.getCurrentSelectedObject()!.filterColor = pickerColor.value;
                                                            simController.currentSim.refresh();
                                                            print("HEX color no alpha ${colorToHex(c, enableAlpha: true)}");
                                                          });
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              )

                                            //CustomColorPickerPage(simController: simController)
                                            : const SizedBox(),
                                      ),
                                      //Blur
                                      Obx(
                                        () => simController.selectedSimObjectIndex.value != -1 &&
                                                simController.selectedType.value != SimObjectType.mask &&
                                                simController.selectedType.value != SimObjectType.text
                                            ? SidebarPanelSlider(
                                                title: "Blur",
                                                controller: blurTextController,
                                                // sliderType: blur,
                                                // simController: simController,
                                                sliderValue: blur,
                                                sidebarSlider: SidebarSliderProperty.blur,
                                                sliderColorType: SliderColorType.noGradient,
                                                minValue: 0,
                                                maxValue: 3,
                                                onChanged: (newBlur) {
                                                  if (newBlur > 3) {
                                                    newBlur = 3;
                                                  }

                                                  if (newBlur < 0) {
                                                    newBlur = 0;
                                                  }
                                                  blur.value = newBlur;
                                                  blurTextController.text = newBlur.toString();
                                                  simController.getCurrentSelectedObject()!.blur = newBlur;
                                                  simController.currentSim.refresh();
                                                },
                                              )
                                            : const SizedBox(),
                                      ),
                                      //Opacity
                                      Obx(
                                        () => Visibility(
                                          visible: simController.selectedSimObjectIndex.value != -1 &&
                                              (simController.selectedType.value == SimObjectType.sprite ||
                                                  simController.selectedType.value == SimObjectType.shape ||
                                                  simController.selectedType.value == SimObjectType.image ||
                                                  simController.selectedType.value == SimObjectType.locationJumper),
                                          child: SidebarPanelSlider(
                                            title: "Opacity",
                                            controller: opacityTextController,
                                            // sliderType: opacity,
                                            // simController: simController,
                                            sliderValue: opacity,
                                            sidebarSlider: SidebarSliderProperty.opacity,
                                            sliderColorType: SliderColorType.bwGradient,
                                            minValue: 0,
                                            maxValue: 1,
                                            onChanged: (newValue) {
                                              if (newValue > 1) {
                                                newValue = 1;
                                              }

                                              if (newValue < 0) {
                                                newValue = 0;
                                              }
                                              opacity.value = newValue;
                                              opacityTextController.text = newValue.toString();
                                              simController.getCurrentSelectedObject()!.opacity = newValue;
                                              simController.currentSim.refresh();
                                            },
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : const SizedBox(),
                        ),
                        // Timing Section
                        Obx(
                          () => simController.selectedSimObjectIndex.value != -1 && simController.selectedType.value != SimObjectType.mask
                              ? SimAnimatedDropdown(
                                  title: "Timing",
                                  body: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      //Fade In When
                                      Obx(
                                        () =>
                                            simController.selectedSimObjectIndex.value != -1 && simController.selectedType.value != SimObjectType.mask
                                                ? SidebarPanelSlider(
                                                    title: "Fade In When",
                                                    controller: fadeInWhenTextController,
                                                    // sliderType: fadeInWhen,
                                                    // simController: simController,
                                                    sliderValue: fadeInWhen,
                                                    sidebarSlider: SidebarSliderProperty.fadeInWhen,
                                                    sliderColorType: SliderColorType.noGradient,
                                                    minValue: 0,
                                                    maxValue: 50,
                                                    textPrecision: 0,
                                                    textUnit: "s",
                                                    onChanged: (newValue) {
                                                      if (newValue > 50) {
                                                        newValue = 50;
                                                      }

                                                      if (newValue < 0) {
                                                        newValue = 0;
                                                      }
                                                      fadeInWhen.value = newValue;
                                                      fadeInWhenTextController.text = newValue.toString();
                                                      simController.getCurrentSelectedObject()!.fadeInWhen = newValue;
                                                      simController.currentSim.refresh();
                                                    },
                                                  )
                                                : const SizedBox(),
                                      ),
                                      //Fade In Duration
                                      Obx(
                                        () =>
                                            simController.selectedSimObjectIndex.value != -1 && simController.selectedType.value != SimObjectType.mask
                                                ? SidebarPanelSlider(
                                                    title: "Fade In Duration",
                                                    controller: fadeInDurationTextController,
                                                    // sliderType: fadeInDuration,
                                                    // simController: simController,
                                                    sliderValue: fadeInDuration,
                                                    sidebarSlider: SidebarSliderProperty.fadeInDuration,
                                                    sliderColorType: SliderColorType.noGradient,
                                                    minValue: 0,
                                                    maxValue: 50,
                                                    textPrecision: 0,
                                                    textUnit: "s",
                                                    onChanged: (newValue) {
                                                      if (newValue > 50) {
                                                        newValue = 50;
                                                      }

                                                      if (newValue < 0) {
                                                        newValue = 0;
                                                      }
                                                      fadeInDuration.value = newValue;
                                                      fadeInDurationTextController.text = newValue.toString();
                                                      simController.getCurrentSelectedObject()!.fadeInDuration = newValue;
                                                      simController.currentSim.refresh();
                                                    },
                                                  )
                                                : const SizedBox(),
                                      ),
                                      //Fade Out In
                                      Obx(
                                        () =>
                                            simController.selectedSimObjectIndex.value != -1 && simController.selectedType.value != SimObjectType.mask
                                                ? SidebarPanelSlider(
                                                    title: "Fade Out In",
                                                    controller: fadeOutWhenTextController,
                                                    // sliderType: fadeOutWhen,
                                                    // simController: simController,
                                                    sliderValue: fadeOutWhen,
                                                    sidebarSlider: SidebarSliderProperty.fadeOutIn,
                                                    sliderColorType: SliderColorType.noGradient,
                                                    minValue: 0,
                                                    maxValue: 50,
                                                    textPrecision: 0,
                                                    textUnit: "s",
                                                    onChanged: (newValue) {
                                                      if (newValue > 50) {
                                                        newValue = 50;
                                                      }

                                                      if (newValue < 0) {
                                                        newValue = 0;
                                                      }
                                                      fadeOutWhen.value = newValue;
                                                      fadeOutWhenTextController.text = newValue.toString();
                                                      simController.getCurrentSelectedObject()!.fadeOutWhen = newValue;
                                                      simController.currentSim.refresh();
                                                    },
                                                  )
                                                : const SizedBox(),
                                      ),
                                      //Fade Out Duration
                                      Obx(
                                        () =>
                                            simController.selectedSimObjectIndex.value != -1 && simController.selectedType.value != SimObjectType.mask
                                                ? SidebarPanelSlider(
                                                    title: "Fade Out Duration",
                                                    controller: fadeOutDurationTextController,
                                                    // sliderType: fadeOutDuration,
                                                    // simController: simController,
                                                    sliderValue: fadeOutDuration,
                                                    sidebarSlider: SidebarSliderProperty.fadeOutDuration,
                                                    sliderColorType: SliderColorType.noGradient,
                                                    minValue: 0,
                                                    maxValue: 50,
                                                    textPrecision: 0,
                                                    textUnit: "s",
                                                    onChanged: (newValue) {
                                                      if (newValue > 50) {
                                                        newValue = 50;
                                                      }

                                                      if (newValue < 0) {
                                                        newValue = 0;
                                                      }
                                                      fadeOutDuration.value = newValue;
                                                      fadeOutDurationTextController.text = newValue.toString();
                                                      simController.getCurrentSelectedObject()!.fadeOutDuration = newValue;
                                                      simController.currentSim.refresh();
                                                    },
                                                  )
                                                : const SizedBox(),
                                      ),

                                      Obx(
                                        () {
                                          final timingMapping = {
                                            "Scenario": "scenario",
                                            "Location": "location",
                                            "State": "state",
                                          };
                                          return simController.selectedSimObjectIndex.value != -1 &&
                                                  simController.selectedType.value != SimObjectType.mask
                                              ? Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    const SizedBox(height: 10),
                                                    //When Timing Begins
                                                    const Text(
                                                      "When Timing Begins",
                                                      style: TextStyle(
                                                        color: Colors.white,
                                                        fontSize: 14,
                                                        fontWeight: FontWeight.w500,
                                                      ),
                                                    ),
                                                    DropdownButton(
                                                      isExpanded: true,
                                                      value: simController.getCurrentSelectedObject()!.trigger,
                                                      items: timingMapping.entries.map((entry) {
                                                        return DropdownMenuItem(
                                                          child: Text(
                                                            entry.key,
                                                            style: dropdownTextStyle.copyWith(
                                                              color: Colors.white,
                                                            ),
                                                          ),
                                                          value: entry.value,
                                                        );
                                                      }).toList(),
                                                      onChanged: (value) {
                                                        simController.getCurrentSelectedObject()!.trigger = value as String;
                                                        simController.currentSim.refresh();
                                                      },
                                                      hint: const Text("When Timing Begins"),
                                                    ),
                                                    const SizedBox(height: 10),
                                                    // Trigger once
                                                    Row(
                                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                      children: [
                                                        const Text(
                                                          "Trigger Once",
                                                          style: TextStyle(
                                                            color: Colors.white,
                                                            fontSize: 14,
                                                            fontWeight: FontWeight.w500,
                                                          ),
                                                        ),
                                                        Switch(
                                                          value: simController.getCurrentSelectedObject()!.triggerOnce,
                                                          onChanged: (value) {
                                                            simController.getCurrentSelectedObject()!.triggerOnce = value;
                                                            simController.currentSim.refresh();
                                                          },
                                                          activeColor: brick,
                                                          activeTrackColor: brick.withOpacity(0.5),
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                )
                                              /* SimAnimatedDropdown(
                                                  breakpoint: breakpoint,
                                                  title: "When Timing Begins",
                                                ) */
                                              : const SizedBox();
                                        },
                                      ),
                                      const SizedBox(height: 10),
                                      // Timer Type
                                      Obx(
                                        () => simController.selectedSimObjectIndex.value != -1 &&
                                                simController.selectedType.value == SimObjectType.timer
                                            ? SizedBox(
                                                width: 270,
                                                child: Row(
                                                  children: [
                                                    const Expanded(
                                                        child:
                                                            Text("Timer Type", style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold))),
                                                    SizedBox(
                                                      width: 110,
                                                      child: DropdownButtonFormField(
                                                        isExpanded: true,
                                                        dropdownColor: lightBackgrounds,
                                                        items: [
                                                          // const DropdownMenuItem(child: Text(""), value: ""),
                                                          ...timerTypesMapping.keys.map(
                                                            (timerType) {
                                                              return DropdownMenuItem(
                                                                child: Text(timerTypesMapping[timerType]!, style: dropdownTextStyle),
                                                                value: timerType,
                                                              );
                                                            },
                                                          ).toList()
                                                        ],
                                                        style: const TextStyle(color: Colors.white),
                                                        onChanged: (value) {
                                                          (simController.getCurrentSelectedObject()! as SimTimer).type =
                                                              getTimerTypeFromString(value as String)!;
                                                          simController.currentSim.refresh();
                                                        },
                                                        value: getTimerTypeString((simController.getCurrentSelectedObject()! as SimTimer).type),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              )
                                            : const SizedBox(),
                                      ),
                                      const SizedBox(height: 10),
                                      // Timer Format
                                      Obx(
                                        () => simController.selectedSimObjectIndex.value != -1 &&
                                                simController.selectedType.value == SimObjectType.timer
                                            ? SizedBox(
                                                width: 270,
                                                child: Row(
                                                  children: [
                                                    const Expanded(
                                                        child:
                                                            Text("Timer Format", style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold))),
                                                    SizedBox(
                                                      width: 110,
                                                      child: DropdownButtonFormField(
                                                        isExpanded: true,
                                                        dropdownColor: lightBackgrounds,
                                                        items: [
                                                          // const DropdownMenuItem(child: Text(""), value: ""),
                                                          ...timerFormatsMapping.keys.map(
                                                            (timerFormat) {
                                                              return DropdownMenuItem(
                                                                child: Text(
                                                                  timerFormatsMapping[timerFormat]!,
                                                                  style: dropdownTextStyle,
                                                                ),
                                                                value: timerFormat,
                                                              );
                                                            },
                                                          ).toList()
                                                        ],
                                                        style: const TextStyle(color: Colors.white),
                                                        onChanged: (value) {
                                                          (simController.getCurrentSelectedObject()! as SimTimer).format =
                                                              getTimerFormatFromString(value as String)!;
                                                          simController.currentSim.refresh();
                                                        },
                                                        value: getTimerFormatString((simController.getCurrentSelectedObject()! as SimTimer).format),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              )
                                            : const SizedBox(),
                                      ),
                                      const SizedBox(height: 10),
                                      // Timer starting seconds
                                      Obx(() {
                                        if (simController.selectedSimObjectIndex.value != -1 &&
                                            simController.selectedType.value == SimObjectType.timer &&
                                            (simController.getCurrentSelectedObject() as SimTimer?)?.type == SimTimerType.countdown) {
                                          final timer = (simController.getCurrentSelectedObject() as SimTimer?);
                                          final controller = TextEditingController(text: timer?.startingSecond.toString());
                                          return Column(
                                            children: [
                                              SizedBox(
                                                width: 270,
                                                child: Row(
                                                  children: [
                                                    const Expanded(
                                                        child: Text("Seconds", style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold))),
                                                    SizedBox(
                                                      width: 110,
                                                      child: TextField(
                                                        controller: controller
                                                          ..selection = TextSelection.fromPosition(TextPosition(offset: controller.text.length)),
                                                        onChanged: (value) {
                                                          (simController.getCurrentSelectedObject()! as SimTimer).startingSecond =
                                                              int.tryParse(value) ?? 0;
                                                          simController.currentSim.refresh();
                                                        },
                                                        keyboardType: TextInputType.number,
                                                        textDirection: TextDirection.ltr,
                                                        style: const TextStyle(color: Colors.white, fontSize: 12),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              const SizedBox(height: 10),
                                            ],
                                          );
                                        }
                                        return const SizedBox();
                                      }),
                                    ],
                                  ),
                                )
                              : const SizedBox(),
                        ),
                        Obx(
                          () => simController.selectedSimObjectIndex.value != -1
                              ? SimAnimatedDropdown(
                                  title: "Details",
                                  defaultOpen: true.obs,
                                  body: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      // To
                                      Obx(
                                        () => simController.selectedSimObjectIndex.value != -1 &&
                                                simController.selectedType.value == SimObjectType.image
                                            ? SizedBox(
                                                width: 270,
                                                child: Row(
                                                  children: [
                                                    DropdownMenu(
                                                      width: 200,
                                                      onSelected: (String? selected) {
                                                        if (selected == null) return;
                                                        (simController.getCurrentSelectedObject()! as SimImage).to = selected;
                                                        simController.currentSim.refresh();
                                                      },
                                                      controller:
                                                          TextEditingController(text: (simController.getCurrentSelectedObject()! as SimImage).to),
                                                      label: const Text("To"),
                                                      textStyle: const TextStyle(color: Colors.white),
                                                      trailingIcon: const Icon(Icons.arrow_drop_down, color: Colors.white),
                                                      dropdownMenuEntries: [
                                                        const DropdownMenuEntry(
                                                          value: "",
                                                          label: "(None)",
                                                        ),
                                                        ...simController.currentSim.value!.locations
                                                            .where((loc) =>
                                                                loc.id !=
                                                                simController.currentSim.value!.locations[simController.currentLocation.value].id)
                                                            .map(
                                                              (loc) => DropdownMenuEntry(
                                                                value: loc.id,
                                                                label:
                                                                    "${loc.name} (${simController.currentSim.value!.states.firstWhere((element) => element.id == loc.state).name})",
                                                              ),
                                                            )
                                                      ].toList(),
                                                    )
                                                  ],
                                                ),
                                              )
                                            : const SizedBox(),
                                      ),
                                      // Location jumper location
                                      Obx(
                                        () => simController.selectedSimObjectIndex.value != -1 &&
                                                simController.selectedType.value == SimObjectType.shape
                                            ? SizedBox(
                                                width: 270,
                                                child: Row(
                                                  children: [
                                                    DropdownMenu(
                                                      width: 200,
                                                      onSelected: (selected) {
                                                        print("Selected location jumper location: $selected");
                                                        if (selected == null) return;
                                                        final s = simController.currentSim.value!.locations[simController.currentLocation.value]
                                                            .shapes[simController.selectedSimObjectIndex.value];
                                                        final jumper = SimLocationJumper(
                                                          to: selected,
                                                          shape: s.shape,
                                                          x: s.x,
                                                          y: s.y,
                                                          width: s.width,
                                                          height: s.height,
                                                          scale: s.scale,
                                                          widthScale: s.widthScale,
                                                          heightScale: s.heightScale,
                                                          filterColor: s.filterColor,
                                                          blur: s.blur,
                                                          mirrorX: s.mirrorX,
                                                          mirrorY: s.mirrorY,
                                                          rotation: s.rotation,
                                                          opacity: s.opacity,
                                                        )..clickable = true;
                                                        simController.currentSim.value!.locations[simController.currentLocation.value].jumpers
                                                            .add(jumper);
                                                        simController.currentSim.value!.locations[simController.currentLocation.value].shapes
                                                            .removeAt(simController.selectedSimObjectIndex.value);
                                                        simController.selectedSimObjectIndex.value = -1;
                                                        simController.selectedType.value = null;
                                                        simController.currentSim.refresh();
                                                      },
                                                      label: const Text("Convert to location jumper"),
                                                      trailingIcon: const Icon(Icons.arrow_drop_down, color: Colors.white),
                                                      // selectedTrailingIcon: const Icon(Icons.arrow_drop_up, color: Colors.white),
                                                      dropdownMenuEntries: simController.currentSim.value!.locations
                                                          .map(
                                                            (loc) => DropdownMenuEntry(
                                                              value: loc.id,
                                                              label:
                                                                  "${loc.name} (${simController.currentSim.value!.states.firstWhere((element) => element.id == loc.state).name})",
                                                            ),
                                                          )
                                                          .toList(),
                                                    )
                                                  ],
                                                ),
                                              )
                                            : const SizedBox(),
                                      ),
                                      // Destination
                                      Obx(
                                        () => simController.selectedSimObjectIndex.value != -1 &&
                                                simController.selectedType.value == SimObjectType.locationJumper
                                            ? Column(
                                                children: [
                                                  SizedBox(
                                                    width: 270,
                                                    height: 80,
                                                    child: Row(
                                                      children: [
                                                        DropdownMenu(
                                                          // width: 200,
                                                          width: 220,
                                                          onSelected: (String? selected) {
                                                            if (selected == null) return;
                                                            simController.currentSim.value!.locations[simController.currentLocation.value]
                                                                .jumpers[simController.selectedSimObjectIndex.value].to = selected;
                                                            simController.currentSim.refresh();
                                                          },
                                                          controller: TextEditingController(
                                                              text: simController.currentSim.value!.locations[simController.currentLocation.value]
                                                                  .jumpers[simController.selectedSimObjectIndex.value].to),
                                                          label: const Text("Destination"),
                                                          textStyle: const TextStyle(color: Colors.white),
                                                          trailingIcon: const Icon(Icons.arrow_drop_down, color: Colors.white),
                                                          // selectedTrailingIcon: const Icon(Icons.arrow_drop_up, color: Colors.white),
                                                          dropdownMenuEntries: simController.currentSim.value!.locations
                                                              .map(
                                                                (loc) => DropdownMenuEntry(
                                                                  value: loc.id,
                                                                  label:
                                                                      "${loc.name} (${simController.currentSim.value!.states.firstWhere((element) => element.id == loc.state).name})",
                                                                ),
                                                              )
                                                              .toList(),
                                                        )
                                                      ],
                                                    ),
                                                  ),
                                                  const SizedBox(height: 10),
                                                ],
                                              )
                                            : const SizedBox(),
                                      ),
                                      // Timeout
                                      Obx(
                                        () => simController.selectedSimObjectIndex.value != -1 &&
                                                simController.selectedType.value == SimObjectType.locationJumper
                                            ? Column(
                                                children: [
                                                  SizedBox(
                                                    width: 270,
                                                    child: TextField(
                                                      decoration: const InputDecoration(hintText: "Seconds", label: Text("Timeout")),
                                                      style: const TextStyle(color: white60),
                                                      controller: timoutTextController
                                                        ..value = TextEditingValue(
                                                            text: simController.currentSim.value!.locations[simController.currentLocation.value]
                                                                .jumpers[simController.selectedSimObjectIndex.value].delay
                                                                .toString()),
                                                      // NOTE: setting the controller to the text field causes the text field to wrongly set the value.
                                                      // A hacky solution is commented below in the onChanged method.
                                                      /* controller: TextEditingController(
                                                      text: simController.selectedSimObjectIndex.value > -1
                                                          ? simController.currentSim.value!.locations[simController.currentLocation.value]
                                                              .jumpers[simController.selectedSimObjectIndex.value].delay
                                                              .toString()
                                                          : "",
                                                    ), */
                                                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                                                      onChanged: (value) {
                                                        if (value.isEmpty) {
                                                          simController.currentSim.value!.locations[simController.currentLocation.value]
                                                              .jumpers[simController.selectedSimObjectIndex.value].delay = 0;
                                                          return;
                                                        } else {
                                                          /* String s = value;
                                                        String firstChar = s[0];
                                                        s = s.substring(1, s.length);
                                                        s += firstChar;
                                                        simController.currentSim.value!.locations[simController.currentLocation.value]
                                                            .jumpers[simController.selectedSimObjectIndex.value].delay = int.parse(s); */
                                                          simController.currentSim.value!.locations[simController.currentLocation.value]
                                                              .jumpers[simController.selectedSimObjectIndex.value].delay = int.parse(value);
                                                        }
                                                        simController.currentSim.refresh();
                                                      },
                                                    ),
                                                  ),
                                                  const SizedBox(height: 10),
                                                ],
                                              )
                                            : const SizedBox(),
                                      ),
                                      // Moveable
                                      Obx(() {
                                        if (simController.selectedSimObjectIndex.value != -1 &&
                                            simController.selectedType.value != SimObjectType.mask &&
                                            simController.selectedType.value != SimObjectType.locationJumper) {
                                          return Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              const Text("Movable", style: TextStyle(color: Colors.white)),
                                              Switch(
                                                  value: simController.getCurrentSelectedObject()!.movable,
                                                  activeColor: const Color(0xffE9BB41),
                                                  inactiveTrackColor: Colors.white.withOpacity(0.5),
                                                  onChanged: (checked) {
                                                    simController.getCurrentSelectedObject()!.movable = checked;
                                                    simController.currentSim.refresh();
                                                  }),
                                            ],
                                          );
                                        }
                                        return const SizedBox();
                                      }),
                                      // Hide on start
                                      Obx(() {
                                        if (simController.selectedSimObjectIndex.value != -1 &&
                                            (simController.selectedType.value == SimObjectType.image ||
                                                simController.selectedType.value == SimObjectType.person)) {
                                          final imgOrPerson = simController.getCurrentSelectedObject()!;
                                          return Row(
                                            children: [
                                              Checkbox(
                                                activeColor: brick,
                                                checkColor: Colors.white,
                                                side: const BorderSide(color: Colors.white),
                                                value: imgOrPerson.hideOnStart,
                                                shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(2))),
                                                onChanged: ((checked) {
                                                  // checked can be null
                                                  if (checked == true) {
                                                    imgOrPerson.clickToToggle = true;
                                                  }
                                                  imgOrPerson.hideOnStart = checked ?? false;
                                                  simController.currentSim.refresh();
                                                }),
                                              ),
                                              const SizedBox(width: 10),
                                              const Expanded(child: Text("Hide on start", style: TextStyle(color: Colors.white))),
                                            ],
                                          );
                                        }
                                        return const SizedBox();
                                      }),
                                      // Click to show/hide
                                      Obx(() {
                                        if (simController.selectedSimObjectIndex.value != -1 &&
                                            (simController.selectedType.value == SimObjectType.image ||
                                                simController.selectedType.value == SimObjectType.person)) {
                                          final simObj = simController.getCurrentSelectedObject()!;
                                          return Row(
                                            children: [
                                              Checkbox(
                                                activeColor: brick,
                                                checkColor: Colors.white,
                                                side: const BorderSide(color: Colors.white),
                                                value: simObj.clickToToggle,
                                                shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(2))),
                                                onChanged: ((checked) {
                                                  final syncedObjects = <SimObject>[];
                                                  if (simObj is SimPerson) {
                                                    for (var person
                                                        in simController.currentSim.value!.locations[simController.currentLocation.value].people) {
                                                      if (person.syncVariable == simObj.id) {
                                                        syncedObjects.add(person);
                                                      }
                                                    }
                                                  }
                                                  // checked can be null
                                                  if (checked == false) {
                                                    simObj.hideOnStart = false;
                                                  }
                                                  simController.getCurrentSelectedObject()!.clickToToggle = checked ?? false;
                                                  simController.currentSim.refresh();
                                                }),
                                              ),
                                              const SizedBox(width: 10),
                                              const Expanded(child: Text("Click to hide/show", style: TextStyle(color: Colors.white))),
                                            ],
                                          );
                                        }
                                        return const SizedBox();
                                      }),
                                      // Shape
                                      Obx(() {
                                        if (simController.selectedType.value == SimObjectType.locationJumper) {
                                          return Column(
                                            children: [
                                              const SizedBox(height: 10),
                                              DropdownMenu<String>(
                                                width: 200,
                                                label: const Text("Shape", style: TextStyle(color: white60)),
                                                dropdownMenuEntries: const [
                                                  DropdownMenuEntry(
                                                    label: "Circle",
                                                    value: "circle",
                                                  ),
                                                  DropdownMenuEntry(
                                                    label: "Rectangle",
                                                    value: "rectangle",
                                                  ),
                                                  DropdownMenuEntry(
                                                    label: "Triangle",
                                                    value: "triangle",
                                                  ),
                                                  DropdownMenuEntry(
                                                    label: "Arrow",
                                                    value: "arrow",
                                                  ),
                                                ],
                                                onSelected: (value) {
                                                  if (value == null) return;
                                                  simController.currentSim.value!.locations[simController.currentLocation.value]
                                                      .jumpers[simController.selectedSimObjectIndex.value].shape = value;
                                                  simController.currentSim.refresh();
                                                },
                                                textStyle: const TextStyle(color: Colors.white),
                                              ),
                                              const SizedBox(height: 10),
                                              Row(
                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                children: [
                                                  const Text("Interactive", style: TextStyle(color: white60)),
                                                  const SizedBox(width: 10),
                                                  Checkbox(
                                                      value: simController.currentSim.value!.locations[simController.currentLocation.value]
                                                          .jumpers[simController.selectedSimObjectIndex.value].clickable,
                                                      onChanged: (value) {
                                                        if (value == null) return;
                                                        simController.currentSim.value!.locations[simController.currentLocation.value]
                                                            .jumpers[simController.selectedSimObjectIndex.value].clickable = value;
                                                        simController.currentSim.refresh();
                                                      })
                                                ],
                                              ),
                                              Obx(
                                                () => simController.selectedSimObjectIndex.value != -1 &&
                                                        simController.selectedType.value == SimObjectType.locationJumper
                                                    ? SizedBox(
                                                        width: 270,
                                                        child: TextButton(
                                                          onPressed: () {
                                                            final jumper = simController
                                                                .currentSim
                                                                .value!
                                                                .locations[simController.currentLocation.value]
                                                                .jumpers[simController.selectedSimObjectIndex.value];
                                                            final shape = SimShape(
                                                              shape: jumper.shape,
                                                              x: jumper.x,
                                                              y: jumper.y,
                                                              width: jumper.width,
                                                              height: jumper.height,
                                                              scale: jumper.scale,
                                                              widthScale: jumper.widthScale,
                                                              heightScale: jumper.heightScale,
                                                              filterColor: jumper.filterColor,
                                                              blur: jumper.blur,
                                                              mirrorX: jumper.mirrorX,
                                                              mirrorY: jumper.mirrorY,
                                                              rotation: jumper.rotation,
                                                              opacity: jumper.opacity,
                                                            );
                                                            simController.currentSim.value!.locations[simController.currentLocation.value].shapes
                                                                .add(shape);
                                                            simController.currentSim.value!.locations[simController.currentLocation.value].jumpers
                                                                .removeAt(simController.selectedSimObjectIndex.value);
                                                            simController.selectedSimObjectIndex.value = -1;
                                                            simController.selectedType.value = null;
                                                            simController.currentSim.refresh();
                                                          },
                                                          child: const Padding(
                                                            padding: EdgeInsets.only(top: 15.0, bottom: 20),
                                                            child: Row(
                                                              mainAxisAlignment: MainAxisAlignment.start,
                                                              children: [
                                                                Text(
                                                                  "Convert to shape",
                                                                  style: TextStyle(color: white60),
                                                                )
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      )
                                                    : const SizedBox(),
                                              ),
                                            ],
                                          );
                                        }
                                        return const SizedBox();
                                      }),
                                      // Recording
                                      Obx(
                                        () {
                                          // final recording = false.obs;
                                          return Visibility(
                                            child: Column(
                                              children: [
                                                Row(
                                                  children: [
                                                    if (recordPath.value.isEmpty && /* just to force refresh */
                                                        recordStatus.value != RecordingStatus.playing)
                                                      Expanded(
                                                        child: Container(
                                                          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                                                          margin: const EdgeInsets.only(right: 10, bottom: 8),
                                                          decoration: BoxDecoration(
                                                            border: Border.all(color: Colors.white),
                                                            borderRadius: BorderRadius.circular(24),
                                                          ),
                                                          child: TextButton(
                                                            onPressed: () async {
                                                              if (!await record.hasPermission()) return;
                                                              if (await record.isRecording()) {
                                                                final path = await record.stop();
                                                                if (path == null) {
                                                                  return print("Recording failed. Recording path empty");
                                                                }
                                                                print(path);
                                                                recordPath.value = path;
                                                                Future.delayed(const Duration(), (() async {
                                                                  print("Updated value:" + recordPath.value);
                                                                }));
                                                              } else {
                                                                final devices = await record.listInputDevices();
                                                                print("Record devices: $devices");
                                                                // final dir = await getApplicationDocumentsDirectory();
                                                                // final recDir = await dir.createTemp("recording_");
                                                                // print(recDir.path);
                                                                await record.start();
                                                                recordStatus.value = RecordingStatus.recording;
                                                                return;
                                                                // path: recDir.path + "recording.m4a",
                                                                // device: devices[0],
                                                              }
                                                            },
                                                            child: recordStatus.value == RecordingStatus.recording
                                                                ? const Row(
                                                                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                                                                    children: [
                                                                      Text("Stop Recording", style: TextStyle(color: Colors.white)),
                                                                      SizedBox(width: 10),
                                                                      Icon(Icons.circle, color: Colors.red),
                                                                    ],
                                                                  )
                                                                : const Row(
                                                                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                                                                    children: [
                                                                      Text("Record", style: TextStyle(color: Colors.white)),
                                                                      SizedBox(width: 10),
                                                                      Icon(Icons.circle_outlined, color: Colors.red),
                                                                    ],
                                                                  ),
                                                          ),
                                                        ),
                                                      ),
                                                    if (recordPath.value.isNotEmpty)
                                                      Expanded(
                                                        child: Obx(
                                                          () => IconButton(
                                                            onPressed: () async {
                                                              if (recordPath.value.isEmpty) return;
                                                              if (playing.value) {
                                                                playing.value = false;
                                                                return audio.pause();
                                                              }
                                                              print("Setting audio source ${recordPath.value}");
                                                              await audio.setSource(DeviceFileSource(recordPath.value));
                                                              playing.value = true;
                                                              audio.onPlayerComplete.listen((_) {
                                                                playing.value = false;
                                                                recordStatus.value = RecordingStatus.stopped;
                                                              });
                                                              return audio.resume();
                                                            },
                                                            icon: playing.value
                                                                ? const Icon(Icons.pause, color: Colors.yellow)
                                                                : Icon(Icons.play_arrow,
                                                                    color: recordPath.value.isEmpty ? Colors.grey : Colors.green),
                                                          ),
                                                        ),
                                                      ),
                                                    if (recordPath.value.isNotEmpty)
                                                      Expanded(
                                                        child: IconButton(
                                                          onPressed: () async {
                                                            if (audio.state == PlayerState.playing) {
                                                              audio.stop();
                                                            }
                                                          },
                                                          icon: Icon(Icons.stop, color: recordPath.value.isEmpty ? Colors.grey : Colors.white),
                                                        ),
                                                      ),
                                                    if (recordPath.value.isNotEmpty)
                                                      Expanded(
                                                        child: IconButton(
                                                          onPressed: () async {
                                                            if (audio.state == PlayerState.playing) {
                                                              audio.stop();
                                                            }
                                                            if (recordPath.value.isNotEmpty) {
                                                              await File(recordPath.value.replaceFirst("file://", "")).delete();
                                                              recordPath.value = "";
                                                            }
                                                            recordStatus.value = RecordingStatus.stopped;
                                                          },
                                                          icon: Icon(Icons.restart_alt, color: recordPath.value.isEmpty ? Colors.grey : Colors.white),
                                                        ),
                                                      )
                                                  ],
                                                ),
                                                Row(
                                                  children: [
                                                    TransparentButton(
                                                      onPressed: () async {
                                                        if (await record.isRecording()) {
                                                          record.stop();
                                                          record.dispose();
                                                        }
                                                        if (recordPath.value.isNotEmpty) {
                                                          await File(recordPath.value.replaceFirst("file://", "")).delete();
                                                          recordPath.value = "";
                                                        }
                                                        recorderVisible.value = false;
                                                        recordStatus.value = RecordingStatus.stopped;
                                                      },
                                                      label: "Cancel",
                                                    ),
                                                    OrangeButton(
                                                      onPressed: () async {
                                                        print("Save record value:" + recordPath.value);
                                                        if (await record.isRecording()) {
                                                          print("Recording in progress");
                                                          return;
                                                        }
                                                        if (recordPath.value == "") {
                                                          print("Record path empty");
                                                          return;
                                                        }
                                                        await audio.release();
                                                        final recDir = await getSimSaveDirectory(simController.currentSim.value!, deep: true);
                                                        final recordName = DateTime.now().toIso8601String() + ".m4a";
                                                        final savedRecordFile = await File(recordPath.value.replaceFirst("file://", ""))
                                                            .copy(path.join(recDir.path, recordName));
                                                        print("\n\n\nSavedRecordFile ${savedRecordFile.absolute}\n\n\n");
                                                        simController.currentSim.value!.locations[simController.currentLocation.value].sounds.add(
                                                          SimSound(
                                                            x: 150 + generateSimObjectDrift() * 150,
                                                            y: 150 + generateSimObjectDrift() * 150,
                                                            path: recordName,
                                                          ),
                                                        );
                                                        recorderVisible.value = false;
                                                        recorderVisible.refresh();
                                                        simController.currentSim.refresh();
                                                      },
                                                      label: "Save",
                                                    )
                                                  ],
                                                )
                                              ],
                                            ),
                                            visible: recorderVisible.value,
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                                )
                              : const SizedBox(),
                        ),
                        const SizedBox(height: 22),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
