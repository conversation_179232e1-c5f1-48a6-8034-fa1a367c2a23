import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/controllers/UserController.dart';
import 'package:simsushare_player/utils/constants.dart';

class RepositoriesDialogLarge extends StatelessWidget {
  RepositoriesDialogLarge({
    Key? key,
  }) : super(key: key);

  final repos = <String>[
    // use base url and strip out the protocol and suffix /api
    baseurl.replaceAll("https://", "").replaceAll("http://", "").replaceAll("/api", ""),
  ].obs;

  final newRepo = "".obs;

  final fieldText = TextEditingController();

  _initialize() async {
    final prefs = await SharedPreferences.getInstance();
    final _userController = Get.find<UserController>();
    final storedRepos = prefs.getStringList("repos_${_userController.user.value["id"]}");
    if (storedRepos != null) {
      repos.value = storedRepos;
    }
  }

  _handleSubmit() async {
    final prefs = await SharedPreferences.getInstance();
    final _userController = Get.find<UserController>();
    if (newRepo.value == "") {
      Get.showSnackbar(const GetSnackBar(
        title: "Error",
        message: "Please enter a repository URL",
        duration: Duration(seconds: 3),
      ));
      return;
    }
    if (repos.contains(newRepo.value.trim())) {
      Get.showSnackbar(const GetSnackBar(
        title: "Error",
        message: "Repository already exists",
        duration: Duration(seconds: 3),
      ));
      return;
    }
    if (newRepo.value.contains("/")) {
      Get.showSnackbar(const GetSnackBar(
        title: "Error",
        message: "Please enter a valid repository domain without http(s):// or slashes",
        duration: Duration(seconds: 3),
      ));
      return;
    }
    // final finalName = newRepo.value.trim().replaceAll("https://", "").replaceAll("http://", "").replaceAll("/api", "");
    final finalName = newRepo.value.trim();
    prefs.setStringList("repos_${_userController.user.value["id"]}", repos..add(finalName));
    newRepo.value = "";
    fieldText.clear();
  }

  @override
  Widget build(BuildContext context) {
    _initialize();
    return ContentDialog(
      title: "Repositories",
      content: Obx(
        () => Column(
          children: [
            ...repos.mapIndexed(
              (index, repo) => Row(
                children: [
                  Expanded(
                    child: Text(
                      repo,
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                  IconButton(
                    onPressed: () async {
                      repos.removeAt(index);
                      final prefs = await SharedPreferences.getInstance();
                      final _userController = Get.find<UserController>();
                      prefs.setStringList("repos_${_userController.user.value["id"]}", repos);
                    },
                    icon: const Icon(
                      Icons.delete,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      hintText: "Add repository",
                      hintStyle: TextStyle(color: Colors.white60),
                    ),
                    style: const TextStyle(color: Colors.white),
                    controller: fieldText,
                    onChanged: (value) {
                      newRepo.value = value;
                    },
                    onSubmitted: (value) {
                      // repos.add(value);
                      _handleSubmit();
                    },
                  ),
                ),
                IconButton(
                  onPressed: () {
                    _handleSubmit();
                  },
                  icon: const Icon(
                    Icons.add,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      actions: [
        TransparentButton(
          label: "Done",
          onPressed: () {
            Get.back();
          },
        ),
        /* OrangeButton(
          label: "Save",
          onPressed: () async {
            final prefs = await SharedPreferences.getInstance();
            final _userController = Get.find<UserController>();
            prefs.setStringList("repos_${_userController.user.value["id"]}", repos);
          },
        ), */
      ],
    );
  }
}
