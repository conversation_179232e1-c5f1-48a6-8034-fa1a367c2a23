import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/pages/home_views/my_simulations_view/update_sim_def_root.dart';
import 'package:simsushare_player/utils/constants.dart';

class RenameDialog extends StatelessWidget {
  final Scenario scenario;

  RenameDialog({
    Key? key,
    required this.scenario,
  }) : super(key: key);

  final name = "".obs;

  @override
  Widget build(BuildContext context) {
    return ContentDialog(
      // width: Get.width * 0.6,
      height: 500,
      title: "Rename Scenario (${scenario.name})",
      content: Column(
        children: [
          const SizedBox(height: 20),
          const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text("Enter a new name for scenario", style: TextStyle(color: white60)),
            ],
          ),
          const SizedBox(height: 20),
          TextField(
            decoration: const InputDecoration(
              hintText: "New scenario name",
              hintStyle: TextStyle(color: white60, fontSize: 14),
              enabledBorder: inputOutlinedBorder,
              focusedBorder: inputOutlinedBorder,
            ),
            style: const TextStyle(color: white60, fontSize: 14),
            onChanged: (value) {
              name.value = value;
              name.refresh();
            },
          )
        ],
        crossAxisAlignment: CrossAxisAlignment.center,
      ),
      actions: [
        Obx(
          () => OrangeButton(
            onPressed: () async {
              late String newPath;
              if (kIsWeb == false) {
                if (name.value.isEmpty) return;
                // check if scenario with same name exists
                if (await Directory(path.join((await getApplicationDocumentsDirectory()).path, "simulations", name.value)).exists()) {
                  Get.snackbar("Error", "Scenario with name ${name.value} already exists", duration: const Duration(seconds: 2)).show();
                  return;
                }
                final oldPath = path.join((await getApplicationDocumentsDirectory()).path, "simulations", scenario.name);
                newPath = path.join((await getApplicationDocumentsDirectory()).path, "simulations", name.value);
                await Directory(oldPath).rename(newPath);
              }
              // TODO: check if this clone is 100% usable
              final clone = scenario.copy(clone: true)..name = name.value;
              // Scenario(name: name.value, locations: [...scenario.locations], currentState: scenario.currentState, masks: [...scenario.masks]);
              if (kIsWeb == false) await updateSimDefRoot(newPath, clone);
              return Get.back(result: clone);
            },
            label: "Rename",
            backgroundColor: name.value.isEmpty ? Colors.grey : brick,
          ),
        ),
      ],
    );
  }
}
