import 'dart:convert';
import 'dart:math';
import 'package:archive/archive.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart' hide FormData, MultipartFile;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/controllers/UserController.dart';
import 'package:simsushare_player/utils/constants.dart';
import 'package:simsushare_player/utils/parsers.dart';
import 'package:string_validator/string_validator.dart';
import 'package:xml/xml.dart' as xml;

// Function to upload a sim in the web version
Future<void> webUploadSim(String url, RxString repo) async {
  // Get the SimController instance
  final c = Get.find<SimController>();

  // Get the first downloaded scenario
  final scenario = c.downloadedSims.values.first;

  // Parse the simdef.xml file
  final simdef = xml.XmlDocument.parse(generateSimDef(scenario));

  // Get the companyId from SharedPreferences
  final prefs = await SharedPreferences.getInstance();
  final companyId = prefs.getString("companyId")!;

  // Get the list of scenario names from the server
  final scenariosResp = await dio.get("/scenarios/$companyId");
  final scenarioNames = (scenariosResp.data["scenarios"] as List<dynamic>).map((e) => e["title"]);

  // Update simdef.xml file --> [simId, simName]
  final simId = simdef.getElement("sim")?.getAttribute("id");
  String simName = simdef.getElement("sim")!.getAttribute("title")!;
  simName = (String name) {
    if (!scenarioNames.contains(name)) return name;
    var i = 1;
    while (scenarioNames.contains("$name ($i)")) {
      i++;
    }
    return "$name ($i)";
  }(simName);
  simdef.getElement("sim")!.setAttribute("id", "${simId}_${(DateTime.now().toUtc().toIso8601String().split(".")..removeLast()).first}");
  simdef.getElement("sim")!.setAttribute("title", simName);
  final updatedSimdef = simdef.toXmlString(pretty: true, indent: "  ");

  // Create a zip archive
  final encoder = ZipEncoder();
  final archive = Archive();
  final dirId = generateRandomString(16);
  archive.addFile(ArchiveFile.string('$dirId/simdef.xml', updatedSimdef));

  // Add image files to the archive
  for (final l in scenario.locations) {
    var base64decode = isBase64(l.image) ? base64Decode(l.image) : l.image.codeUnits;
    archive.addFile(ArchiveFile(
      dirId + '/' + (l.imageFileName ?? l.id),
      base64decode.length,
      base64decode,
    ));
  }

  // Encode the archive into bytes
  OutputStream outputStream = OutputStream(
    byteOrder: LITTLE_ENDIAN,
  );
  List<int>? bytes = encoder.encode(
    archive,
    modified: DateTime.now(),
    output: outputStream,
  );

  // Get the UserController instance
  final _userController = Get.find<UserController>();

  // Send the zip file to the server using Dio
  final response = await Dio(BaseOptions(
    validateStatus: (status) => true,
  )).post(
    "https://" +
        repo.value.substring(0, repo.value.endsWith("/") ? repo.value.length - 1 : repo.value.length) +
        '/api/scenarios/${_userController.user.value["company"]["_id"]}',
    data: FormData.fromMap(
      {
        "scenario": MultipartFile.fromBytes(bytes!, filename: "${scenario.id}.zip"),
      },
    ),
    options: Options(
      headers: {
        "Authorization": _userController.token.value,
      },
    ),
  );

  // When upload is completed, delete temporary directory
  print("Upload complete");

  // If it's failed to upload scenario, then show snackbar message for 3 seconds
  if (response.statusCode == null || response.statusCode! >= 400) {
    print("Failed to upload scenario: ${response.statusCode} ${response.data}");
    Get.showSnackbar(const GetSnackBar(
      title: "Error",
      message: "Failed to upload scenario",
      duration: Duration(seconds: 3),
    ));
    return;
  }

  // If it's succeeded to upload scenario, then show snackbar message for 3 seconds
  Get.showSnackbar(const GetSnackBar(
    title: "Success",
    message: "Scenario Uploaded",
    duration: Duration(seconds: 3),
  ));
}

// Function to generate a random string of given length
String generateRandomString(int length) {
  final random = Random();
  const availableChars = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
  final randomString = List.generate(length, (index) => availableChars[random.nextInt(availableChars.length)]).join();
  return randomString;
}
