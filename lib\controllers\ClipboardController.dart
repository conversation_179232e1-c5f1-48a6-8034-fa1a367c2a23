import 'package:get/get.dart';
import 'package:simsushare_player/models/Mask.dart';
import 'package:simsushare_player/models/SimObjects.dart';

class ClipboardController extends GetxController {
  final simObjects = <SimObject>[].obs;
  final selectedObjects = <SimObject>[].obs;
  final masks = <Mask>[].obs;
  final selectedMasks = <Mask>[].obs;

  clearClipboard() {
    simObjects.clear();
    masks.clear();
  }

  clearSelection() {
    selectedObjects.clear();
    selectedMasks.clear();
  }
}
