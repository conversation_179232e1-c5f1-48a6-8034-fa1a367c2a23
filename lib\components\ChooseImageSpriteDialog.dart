import 'dart:io';
import 'dart:math';
import 'package:file_picker/file_picker.dart' show FileType;

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/utils/picker.dart';

class ChooseImageSpriteDialog extends StatelessWidget {
  ChooseImageSpriteDialog({Key? key}) : super(key: key);

  final selectedFile = Rxn<File>();

  @override
  Widget build(BuildContext context) {
    final SimController _simController = Get.find();
    return Dialog(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          ElevatedButton(
            onPressed: () async {
              final filesResult = await pickFiles(type: FileType.image, allowMultiple: false);
              if (filesResult == null) {
                return;
              }
              selectedFile.value = File(filesResult.files.single.path!);
              // final image = filesResult.files[0];
            },
            child: const Text("Select Image"),
          ),
          Obx(
            () => selectedFile.value != null
                ? SizedBox(
                    child: Image.file(selectedFile.value!),
                    height: Get.height * 0.5,
                    width: Get.width * 0.5,
                  )
                : const SizedBox(
                    child: Text("No Image Selected"),
                  ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                onPressed: () {
                  Get.back();
                },
                child: const Text("Cancel"),
              ),
              ElevatedButton(
                onPressed: () async {
                  if (selectedFile.value == null) {
                    return;
                  }
                  final imageBytes = await selectedFile.value!.readAsBytes();
                  print("Adding sprite");
                  final img = await decodeImageFromList(imageBytes);
                  // print("META: ${(meta["frames"] as Map<String, dynamic>).keys.toList()}");
                  final ratio = img.width / img.height;
                  double imgWidth = img.width.toDouble();
                  double imgHeight = img.height.toDouble();
                  if (ratio > 1) {
                    imgWidth = max(imgWidth, Get.width * 0.3);
                    imgHeight = imgWidth * ratio;
                  } else {
                    imgHeight = max(imgHeight, Get.height * 0.3);
                    imgWidth = imgHeight * ratio;
                  }
                  final frames = [SpriteFrame(x: 0, y: 0, width: imgWidth, height: imgHeight, rotated: false)];
                  final cs = _simController.currentSim.value!;
                  cs.locations[_simController.currentLocation.value].sprites.add(
                    SimSprite(
                      img: img, frames: frames, x: Get.width * 0.2, y: Get.height * 0.2, assetName: "",
                      // width: frames[0].width,
                      // height: frames[0].height,
                    ),
                  );
                  // _simController.updateCurrentSim(cs);
                  _simController.currentSim.value = cs;
                  _simController.currentSim.refresh();
                  // Navigator.pop(context);
                  Get.back();
                },
                child: const Text("Confirm Selection"),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
