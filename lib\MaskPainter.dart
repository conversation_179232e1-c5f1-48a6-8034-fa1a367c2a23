import 'package:flutter/material.dart';
import 'package:simsushare_player/models/Mask.dart';
import 'package:point_in_polygon/point_in_polygon.dart' as pip;

class MaskPainter extends CustomPainter {
  Mask mask;
  BuildContext innerContext;
  Rect rect;

  MaskPainter({required this.mask, required this.innerContext, required this.rect});

  @override
  void paint(Canvas canvas, Size size) {
    final coordinates = mask.coordinates;
    final color = Color(int.tryParse(mask.color, radix: 16) ?? 0xff000000);
    canvas.restore();
    // canvas.saveLayer(Rect.largest, Paint());
    print({"rect": rect});
    canvas.saveLayer(rect, Paint());
    /* coordinates.forEachIndexed((index, coor) {
      if (index == coordinates.length - 1) return;
      final nextCoor = coordinates[index + 1];
      canvas.drawLine(Offset(coor.x, coor.y), Offset(nextCoor.x, nextCoor.y), Paint());
    }); */
    final paintMask = Paint()
      ..strokeWidth = mask.type == MaskType.showOutside ? 3 : 5
      ..style = mask.type == MaskType.showOutside ? PaintingStyle.fill : PaintingStyle.stroke
      ..color = color;
    final path = Path();
    if (coordinates.length == 1) {
      canvas.drawCircle(Offset(coordinates[0].x * size.width, coordinates[0].y * size.height), 3, paintMask);
      return;
    }
    if (coordinates.length == 2) {
      RenderBox? renderbox = innerContext.findRenderObject() as RenderBox?;
      final pos0 = renderbox!.globalToLocal(Offset(coordinates[0].x * size.width, coordinates[0].y * size.height));
      final pos1 = renderbox.globalToLocal(Offset(coordinates[1].x * size.width, coordinates[1].y * size.height));
      // canvas.drawLine(Offset(coordinates[0].x * size.width, coordinates[0].y * size.height),
      //     Offset(coordinates[1].x * size.width, coordinates[1].y * size.height), paintMask);
      canvas.drawLine(Offset(pos0.dx, pos0.dy), Offset(pos1.dx, pos1.dy), paintMask);
      return;
    }
    path.addPolygon(coordinates.map((coor) => Offset(coor.x, coor.y)).toList(), true);
    print(coordinates.map((coor) => Offset(coor.x, coor.y)).toList());
    // canvas.drawPath(path, paintMask);
    canvas.drawPath(path, paintMask);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }

  @override
  bool? hitTest(Offset position) {
    // return super.hitTest(position);
    // print("Position: x: ${position.dx}, y: ${position.dy}");
    if (mask.coordinates.length < 3) return false;
    final touchPoint = pip.Point(x: position.dx, y: position.dy);
    final polygon = mask.coordinates.map((coor) => pip.Point(x: coor.x, y: coor.y)).toList();
    return pip.Poly.isPointInPolygon(touchPoint, polygon);
  }
}
