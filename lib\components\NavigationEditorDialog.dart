import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/utils/constants.dart';

class NavigationEditorDialog extends StatelessWidget {
  final String locId;
  final String direction;

  NavigationEditorDialog({
    Key? key,
    required this.locId,
    required this.direction,
  }) : super(key: key);

  static final inverseDirection = {
    "N": "S",
    "S": "N",
    "E": "W",
    "W": "E",
    "NE": "SW",
    "SE": "NW",
    "NW": "SE",
    "SW": "NE",
    "UP": "DOWN",
    "DOWN": "UP",
  };

  final selectedLocationId = "".obs;
  final withReciprocal = true.obs;

  @override
  Widget build(BuildContext context) {
    final _simController = Get.find<SimController>();
    final simValue = _simController.currentSim.value!;
    selectedLocationId.value =
        _simController.currentSim.value!.navigations.firstWhereOrNull((nav) => nav.from == locId && nav.direction == direction)?.to ?? "";
    return ContentDialog(
      title: "Edit Navigation",
      content: Column(
        children: [
          Obx(
            () => Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: simValue.locations
                      // .whereIndexed((index, loc) => index != _simController.currentLocation.value)
                      .where((loc) => loc.id != locId)
                      .map(
                        (loc) => ListTile(
                          title: Text(
                            "${loc.name} (${simValue.states.firstWhere((state) => loc.state == state.id).name})",
                            style: const TextStyle(color: Colors.white),
                          ),
                          selected: loc.id == selectedLocationId.value,
                          selectedTileColor: white60,
                          selectedColor: Colors.black,
                          onTap: () {
                            if (loc.id == selectedLocationId.value) {
                              selectedLocationId.value = "";
                              return;
                            }
                            selectedLocationId.value = loc.id;
                          },
                        ),
                      )
                      .toList(),
                ),
              ),
            ),
          ),
          const Divider(color: Colors.white),
          Obx(
            () => SwitchListTile(
              value: withReciprocal.value,
              onChanged: ((value) => withReciprocal.value = value),
              title: const Text(
                "With Reciprocal",
                style: TextStyle(color: Colors.white),
              ),
              inactiveTrackColor: white60,
              inactiveThumbColor: white60,
              activeColor: brick,
              activeTrackColor: HSLColor.fromColor(brick).withLightness(HSLColor.fromColor(brick).lightness * 1.35).toColor(),
            ),
          )
        ],
      ),
      actions: [
        TransparentButton(
          onPressed: () {
            Get.back();
          },
          label: "Cancel",
        ),
        OrangeButton(
          onPressed: () {
            if (selectedLocationId.value.isEmpty) {
              _simController.currentSim.value!.navigations.removeWhere((nav) => nav.from == locId && nav.direction == direction);
              Get.back(result: true);
              return;
            }

            final conflictNavigation = simValue.navigations.firstWhereOrNull((nav) => nav.from == locId && nav.direction == direction);
            if (conflictNavigation != null) {
              simValue.navigations.remove(conflictNavigation);
            }
            _simController.currentSim.value!.navigations.add(SimulationNavigation(direction: direction, from: locId, to: selectedLocationId.value));
            if (withReciprocal.value) {
              final conflictReciprocalNavigation = simValue.navigations
                  .firstWhereOrNull((nav) => nav.from == selectedLocationId.value && nav.direction == inverseDirection[direction]);
              if (conflictNavigation != null) {
                simValue.navigations.remove(conflictReciprocalNavigation);
              }
              _simController.currentSim.value!.navigations.add(SimulationNavigation(
                direction: inverseDirection[direction]!,
                from: selectedLocationId.value,
                to: locId,
              ));
            }
            Get.back(result: true);
          },
          label: "Save",
        ),
      ],
    );
  }
}
